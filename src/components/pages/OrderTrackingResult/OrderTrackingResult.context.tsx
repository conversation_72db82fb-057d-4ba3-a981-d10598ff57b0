'use client';

import { parseCookies } from 'nookies';
import { ReactNode, useCallback, useState } from 'react';

import { Order } from '~/data/models/Order';
import { OrderProduct } from '~/data/models/OrderProduct';
import { OrderTrackingInput } from '~/data/models/OrderTrackingInput';
import { ReturnReason } from '~/data/models/ReturnReason';
import { ReturnRequestInput } from '~/data/models/ReturnRequestInput';
import { SiteCartBillingResponse } from '~/data/models/SiteCartBillingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import { WalmartOrderTrackingInput } from '~/data/models/WalmartOrderTrackingInput';
import { apiGetSiteCartBilling } from '~/lib/api/checkout/cart-billing';
import { apiGetSiteCartSummary } from '~/lib/api/checkout/cart-summary';
import { apiDeleteMyOrder } from '~/lib/api/delete-my-order';
import { apiGetReturnReasons } from '~/lib/api/get-return-reasons';
import { apiSendCancelRequest } from '~/lib/api/send-cancel-request';
import { apiSendEmailReceipt } from '~/lib/api/send-email-receipt';
import { apiSendReturnRequest } from '~/lib/api/send-return-request';
import { apiGetOrderTracking } from '~/lib/api/track-order';
import { apiGetWalmartOrderTracking } from '~/lib/api/walmart-track-order';
import { COOKIES } from '~/lib/constants/cookies';
import {
  PROPERTIES as SESSION_PROPERTIES,
  SESSION_STORAGE,
} from '~/lib/constants/sessionStorage';
import { TIME } from '~/lib/constants/time';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { seStorage } from '~/lib/utils/browser-storage';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';
import { isOTSDeployment } from '~/lib/utils/deploy';

interface Props {
  children: ReactNode;
}
interface RequestType {
  type: string;
}

type ReturnReasonDataProps = OrderProduct & OrderTrackingInput;
type ReturnRequestProps = ReturnRequestInput & RequestType;

export interface OrderTrackingResultContextProps {
  cartSummary: SiteCartSummary | null;
  emailSent: boolean;
  errorInReturnReasons: boolean;
  getCartSummary: (cartId: string) => void;
  getOrderTracking: ({ orderId, zip }: OrderTrackingInput) => void;
  getReturnReasons: ({
    productId,
    image,
    name,
    quantity,
    zip,
    orderId,
  }: ReturnReasonDataProps) => void;
  getSiteCartBilling: (cartId: string) => void;
  getWalmartOrderTracking: ({
    orderId,
    trackingId,
  }: WalmartOrderTrackingInput) => void;
  hasError: boolean;
  isLoadingCancelRequest: boolean;
  isLoadingOrder: boolean;
  isLoadingReturnReasons: boolean;
  isSendingEmail: boolean;
  isSendingReturnOrCancelReq: boolean;
  order: Order | null;
  orderCancelled: boolean;
  pdfDownloaded: boolean;
  returnOrCancelReqError: boolean;
  returnOrCancelReqSent: boolean;
  returnReasons: Array<ReturnReason>;
  returnTireData: ReturnReasonDataProps | null;
  sendCancelRequest: ({ orderId, zip }: OrderTrackingInput) => void;
  sendEmailReceipt: ({ orderId, zip }: OrderTrackingInput) => void;
  sendReturnRequest: ({
    orderId,
    zip,
    productId,
    body,
  }: ReturnRequestProps) => void;
  setPDFdownloaded: (value: boolean) => void;
  siteCartBilling: SiteCartBillingResponse | null;
}

const OrderTrackingResultContext =
  createContext<OrderTrackingResultContextProps>();

function useContextSetup() {
  const [order, setOrder] = useState<Order | null>(null);
  const [cartSummary, setCartSummary] = useState<SiteCartSummary | null>(null);
  const [siteCartBilling, setSiteCartBilling] =
    useState<SiteCartBillingResponse | null>(null);
  const [isLoadingReturnReasons, setIsloadingReturnReasons] =
    useState<boolean>(false);
  const [errorInReturnReasons, setErrorForReturnReasons] =
    useState<boolean>(false);
  const [returnTireData, setReturnData] =
    useState<ReturnReasonDataProps | null>(null);
  const [returnReasons, setReturnReasons] = useState<Array<ReturnReason>>([]);
  const [isLoadingOrder, setIsLoadingOrder] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);

  const [isSendingEmail, setIsSendingEmail] = useState<boolean>(false);
  const [emailSent, setEmailSent] = useState<boolean>(false);

  const [pdfDownloaded, setPDFdownloaded] = useState<boolean>(false);

  const [isSendingReturnOrCancelReq, setReturnOrCancelReqLoading] =
    useState<boolean>(false);
  const [returnOrCancelReqSent, setReturnOrCancelReqStatus] =
    useState<boolean>(false);
  const [returnOrCancelReqError, setReturnOrCancelRequestError] =
    useState<boolean>(false);
  const [isLoadingCancelRequest, setIsLoadingCancelRequest] =
    useState<boolean>(false);
  const [orderCancelled, setOrderCancelStatus] = useState<boolean>(false);
  const cookies = parseCookies();

  const sendCancelRequest = useCallback(
    async ({ orderId, zip, isDealerTire }: OrderTrackingInput) => {
      setIsLoadingCancelRequest(true);
      setReturnOrCancelRequestError(false);
      setOrderCancelStatus(false);
      const params = {
        orderId: String(orderId),
        zip: String(zip),
        fromTmo: '0',
      };
      const response = await apiDeleteMyOrder(params);
      const widgetSource = seStorage.getItem(
        SESSION_STORAGE[SESSION_PROPERTIES.WIDGET_SOURCE],
      );
      const orderSource = isDealerTire
        ? 'dt-epp'
        : isOTSDeployment()
          ? 'ots-honda'
          : widgetSource === null
            ? 'simpletire'
            : 'widget';
      if (response.isSuccess) {
        rudderstackSendTrackEvent(RudderstackTrackEventName.ORDER_CANCELLED, {
          orderId,
          zip,
          order_source: orderSource,
        });
        setOrderCancelStatus(true);
        setIsLoadingCancelRequest(false);
        setTimeout(() => {
          window.location.reload();
        }, TIME.MS2000);
        return;
      }
      setOrderCancelStatus(true);
      setIsLoadingCancelRequest(false);
      setTimeout(() => {
        window.location.reload();
      }, TIME.MS2000);
      return;
    },
    [],
  );

  const sendReturnRequest = useCallback(
    async ({ orderId, zip, productId, body, type }: ReturnRequestProps) => {
      setReturnOrCancelReqLoading(true);
      setReturnOrCancelRequestError(false);
      const res =
        type === 'return'
          ? await apiSendReturnRequest({
              orderId,
              zip,
              productId,
              body,
            })
          : await apiSendCancelRequest({
              orderId,
              zip,
              productId,
              body,
            });
      if (res.isSuccess) {
        setReturnOrCancelRequestError(false);
        setReturnOrCancelReqLoading(false);
        setReturnOrCancelReqStatus(true);
        return;
      }
      setReturnOrCancelRequestError(true);
      setReturnOrCancelReqLoading(false);
      setReturnOrCancelReqStatus(false);
    },
    [],
  );

  const getReturnReasons = useCallback(
    async (productDetails: ReturnReasonDataProps) => {
      setIsloadingReturnReasons(true);
      setErrorForReturnReasons(false);

      const res = await apiGetReturnReasons();
      if (res.isSuccess) {
        setErrorForReturnReasons(false);
        setReturnData({
          ...productDetails,
        });

        setReturnReasons(res.data!['orderReturnReasons']);
        setIsloadingReturnReasons(false);
        return;
      }

      setIsloadingReturnReasons(false);
      setErrorForReturnReasons(true);
    },
    [],
  );

  const sendEmailReceipt = useCallback(
    async ({ orderId, zip }: OrderTrackingInput) => {
      setIsSendingEmail(true);
      const res = await apiSendEmailReceipt({
        orderId,
        zip,
      });
      if (res.isSuccess) {
        setIsSendingEmail(false);
        setEmailSent(true);
        return;
      }
      setIsSendingEmail(false);
      setEmailSent(false);
    },
    [],
  );

  const getOrderTracking = useCallback(
    async ({ orderId, zip }: OrderTrackingInput) => {
      setIsLoadingOrder(true);
      if (hasError) {
        setHasError(false);
      }

      const res = await apiGetOrderTracking({
        orderId,
        zip,
      });

      if (res.isSuccess) {
        setOrder(res.data.order);
        setIsLoadingOrder(false);
        return;
      }

      setIsLoadingOrder(false);
      setHasError(true);
    },
    [hasError],
  );

  const getWalmartOrderTracking = useCallback(
    async ({ orderId, trackingId }: WalmartOrderTrackingInput) => {
      setIsLoadingOrder(true);
      if (hasError) {
        setHasError(false);
      }

      const res = await apiGetWalmartOrderTracking({
        orderId,
        trackingId,
      });

      if (res.isSuccess) {
        setOrder(res.data.order);
        setIsLoadingOrder(false);
        return;
      }

      setIsLoadingOrder(false);
      setHasError(true);
    },
    [hasError],
  );

  const getCartSummary = useCallback(
    async (cartId: string) => {
      setIsLoadingOrder(true);
      if (hasError) {
        setHasError(false);
      }

      const response = await apiGetSiteCartSummary({
        query: {
          id: cartId,
          vwo_user: cookies[COOKIES.VWO],
        },
        includeUserRegion: true,
        includeUserZip: true,
      });
      if (response.isSuccess) {
        setCartSummary(response.data.siteCart);
        setIsLoadingOrder(false);
        return;
      }

      setIsLoadingOrder(false);
      setHasError(true);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [hasError],
  );

  const getSiteCartBilling = useCallback(
    async (cartId: string) => {
      setIsLoadingOrder(true);
      if (hasError) {
        setHasError(false);
      }

      const response = await apiGetSiteCartBilling({
        query: { cartId },
      });
      if (response.isSuccess) {
        setSiteCartBilling(response.data.siteCartBillingResponse);
        setIsLoadingOrder(false);
        return;
      }

      setIsLoadingOrder(false);
      setHasError(true);
    },
    [hasError],
  );

  return {
    cartSummary,
    emailSent,
    errorInReturnReasons,
    getCartSummary,
    getOrderTracking,
    getReturnReasons,
    getSiteCartBilling,
    getWalmartOrderTracking,
    hasError,
    isLoadingCancelRequest,
    isLoadingOrder,
    isLoadingReturnReasons,
    isSendingEmail,
    isSendingReturnOrCancelReq,
    order,
    orderCancelled,
    pdfDownloaded,
    returnOrCancelReqError,
    returnOrCancelReqSent,
    returnReasons,
    returnTireData,
    sendCancelRequest,
    sendEmailReceipt,
    sendReturnRequest,
    setPDFdownloaded,
    siteCartBilling,
  };
}

export function OrderTrackingResultContextProvider({ children }: Props) {
  const value = useContextSetup();

  return (
    <OrderTrackingResultContext.Provider value={value}>
      {children}
    </OrderTrackingResultContext.Provider>
  );
}

export const useOrderTrackingResultContextSelector = <SelectedValue,>(
  selector: Selector<OrderTrackingResultContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<OrderTrackingResultContextProps, SelectedValue>(
    OrderTrackingResultContext,
    selector,
    equalCompareFn,
  );
