import lscache from 'lscache';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { useCartUserActionContextSelector } from '~/components/modules/Cart/CartUserAction.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { ShippingType } from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummaryRequest } from '~/data/models/SiteCartSummaryRequest';
import { PAYMENT_OPTIONS } from '~/data/models/SitePaymentOption';
import useRouter from '~/hooks/useRouter';
import { apiUpdateSiteCartSummary } from '~/lib/api/checkout/cart-summary';
import { apiSendEmailQuote } from '~/lib/api/sales-quote';
import { LOCAL_STORAGE, PROPERTIES } from '~/lib/constants/localStorage';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';

import QuoteModal from '../../Payments/QuoteModal/QuoteModal';
import { usePaymentContextSelector } from '../PaymentContext/Payment.context';
import PaymentActionBar from './PaymentActionBar';

interface PaymentActionBarForMainSiteProps {
  isSimpleSalesToolUser: boolean;
  selectedPaymentMethod: PAYMENT_OPTIONS;
}

export default function PaymentActionBarForMainSite({
  isSimpleSalesToolUser,
  selectedPaymentMethod,
}: PaymentActionBarForMainSiteProps) {
  const userInfoFormValues = usePaymentContextSelector(
    (v) => v.userInfoFormValues,
  );
  const { cleanupCart, isFedexTabSelected, setActiveOption } =
    useCartUserActionContextSelector((v) => ({
      cleanupCart: v.cleanupCart,
      isFedexTabSelected: v.isFedexTabSelected,
      setActiveOption: v.setActiveOption,
    }));
  const localVehicleData = useUserPersonalizationContextSelector(
    (v) => v.vehicle,
  );
  const cartSummaryId = useCartSummaryContextSelector(
    (v) => v.cartSummary?.id + '',
  );

  const router = useRouter();
  const searchParams = useSearchParams();
  const widgetSource = searchParams?.get('widgetSource')?.toLowerCase();
  const widgetSourceId = searchParams?.get('widgetSourceId')?.toLowerCase();

  const [isOpenQuoteModal, setIsOpenQuoteModal] = useState<boolean>(false);
  const [isQuoteSending, setIsQuoteSending] = useState<boolean>(false);
  const [quoteSentSuccess, setQuoteSentSuccess] = useState<boolean>(false);
  const [toastMessage, setToastMessage] = useState<string>('');

  const {
    vehicleMake = '',
    vehicleModel = '',
    vehicleTrim = '',
    vehicleYear = '',
  } = localVehicleData ?? {};

  const isRetrieveQuote =
    lscache.get(LOCAL_STORAGE[PROPERTIES.RETRIEVE_QUOTE]) === 'yes';

  const closeQuoteModal = useCallback(async () => {
    if (quoteSentSuccess) {
      setIsOpenQuoteModal(false);
      cleanupCart();
      await router.push(ROUTE_MAP[ROUTES.HOME]);
    } else {
      setIsOpenQuoteModal(false);
    }
  }, [cleanupCart, quoteSentSuccess, router]);

  const sendQuote = useCallback(async () => {
    setIsQuoteSending(true);

    if (!userInfoFormValues.email && !userInfoFormValues.phone) {
      return;
    }

    const requestBody: SiteCartSummaryRequest = {
      email: userInfoFormValues.email,
      vehicleMake,
      vehicleModel,
      vehicleTrim,
      vehicleYear,
      ...(widgetSource ? { widgetSource } : {}),
      ...(widgetSourceId ? { widgetSourceId } : {}),
    };

    const response = await apiUpdateSiteCartSummary({
      query: {
        id: cartSummaryId,
      },
      input: requestBody,
      includeUserRegion: true,
      includeUserZip: true,
    });

    if (response.isSuccess) {
      const res = await apiSendEmailQuote({
        input: {
          cartId: cartSummaryId,
          email: userInfoFormValues.email,
          phone: userInfoFormValues.phone,
        },
        includeUserRegion: true,
        includeUserZip: true,
      });
      if (res.isSuccess) {
        setIsQuoteSending(false);
        setQuoteSentSuccess(res.data.Success);
        setToastMessage(res.data.Message);
        setIsOpenQuoteModal(true);
      }
    }
  }, [
    cartSummaryId,
    userInfoFormValues.email,
    userInfoFormValues.phone,
    vehicleMake,
    vehicleModel,
    vehicleTrim,
    vehicleYear,
    widgetSource,
    widgetSourceId,
  ]);

  useEffect(() => {
    if (isRetrieveQuote && isFedexTabSelected) {
      setActiveOption(ShippingType.FEDEX);
    }
  }, [isRetrieveQuote, isFedexTabSelected, setActiveOption]);

  return (
    <>
      <PaymentActionBar
        isQuoteSending={isQuoteSending}
        paymentType={selectedPaymentMethod}
        isSimpleSalesToolUser={isSimpleSalesToolUser}
        sendQuote={sendQuote}
      />
      <QuoteModal
        isOpen={isOpenQuoteModal}
        message={toastMessage}
        quoteSend={quoteSentSuccess}
        onClose={closeQuoteModal}
      />
    </>
  );
}
