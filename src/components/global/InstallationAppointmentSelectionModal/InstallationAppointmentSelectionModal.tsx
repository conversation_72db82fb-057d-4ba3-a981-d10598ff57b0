import { useSearchParams } from 'next/navigation';
import {
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useCartShippingContextSelector } from '~/components/modules/Cart/CartShipping.context';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { calculateTotalNumberOfTiresInCart } from '~/components/modules/Cart/CartSummaryModal/CarSummaryModal.utils';
import { useModalContextSelector } from '~/context/Modal.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { SiteCartSummaryRequest } from '~/data/models/SiteCartSummaryRequest';
import { SiteProductSubType } from '~/data/models/SiteProductLineSizeDetail';
import { useApiDataWithDefault } from '~/hooks/useApiDataWithDefault';
import useWidgetSource from '~/hooks/useWigetSource';
import {
  apiCreateCartServices,
  apiGetCartServices,
} from '~/lib/api/checkout/cart-services';
import { apiGetInstallerServices } from '~/lib/api/installer-services';
import { PROPERTIES, SESSION_STORAGE } from '~/lib/constants/sessionStorage';
import { STATIC_MODAL_IDS } from '~/lib/constants/staticModals';
import { TIME } from '~/lib/constants/time';
import { Z_INDEX } from '~/lib/constants/zindex';
import logger from '~/lib/helpers/logger';
import { scrollToRef } from '~/lib/helpers/scroll';
import { seStorage } from '~/lib/utils/browser-storage';
import { isOTSDeployment, isSimpleShopDeployment } from '~/lib/utils/deploy';
import { formatDollars } from '~/lib/utils/string';
import { ui } from '~/lib/utils/ui-dictionary';

import Icon from '../Icon/Icon';
import { ICONS } from '../Icon/Icon.constants';
import ShopNameAddress from '../InstallationShopCard/ShopNameAddress';
import Markdown from '../Markdown/Markdown';
import BottomCardModal from '../Modal/BottomCardModal';
import AppointmentDateTimePicker from './AppointmentDateTimePicker';
import { SelectedDayAndTime } from './AppointmentDateTimePicker.types';
import DescriptionWithIcon from './DescriptionWithIcon/DescriptionWithIcon';
import styles from './InstallationAppointmentSelectionModal.styles';
import {
  InstallationAppointmentSelectionModalProps,
  SelectedServiceOffering,
  ServiceOffering,
  ShippingConfirmFormProps,
  SiteInstallerServices,
} from './InstallationAppointmentSelectionModal.types';
import ServiceOfferingsList from './ServiceOfferingsList/ServiceOfferingsList';
import ShippingConfirmForm from './ShippingConfirmForm/ShippingConfirmForm';
import { ShippingConfirmFormValue } from './ShippingConfirmForm/ShippingConfirmForm.types';

function InstallationAppointmentSelectionModal({
  isOpen,
  siteInstallerScheduleResponse,
  onClose,
  initialValue,
  shops,
  cancelAppointment,
  onConfirm,
  productSubType,
}: InstallationAppointmentSelectionModalProps) {
  const {
    installerScheduleMetadata: { installerId },
    siteInstallerSchedule,
  } = siteInstallerScheduleResponse ?? {
    installerScheduleMetadata: { installerId: '' },
    siteInstallerSchedule: { scheduleDays: [] },
  };
  const openStaticModal = useModalContextSelector((v) => v.openStaticModal);

  const shippingFormRef = useRef(null);
  const modalContentRef = useRef<HTMLDivElement>(undefined);

  const userDetail = useUserPersonalizationContextSelector((v) => v.userDetail);
  const { selectedDayAndTime, setSelectedInstaller, setSelectedServices } =
    useCartShippingContextSelector((v) => ({
      selectedDayAndTime: v.selectedDayAndTime,
      setSelectedInstaller: v.setSelectedInstaller,
      setSelectedServices: v.setSelectedServices,
    }));

  const isOTS = isOTSDeployment();
  const isSimpleShop = isSimpleShopDeployment();
  const { cartId, email, siteProducts } = useCartSummaryContextSelector(
    (v) => ({
      cartId: v.cartId,
      email: v.email,
      siteProducts: v.cartSummary?.siteProducts,
    }),
  );
  const [hasError, setHasError] = useState(false);
  const selectedShop = shops && shops.find((shop) => shop.id === installerId);
  const defaultSchedule = selectedShop?.schedule;
  const { isSourcePirelliWidget } = useWidgetSource();

  const query = useMemo(
    () => ({
      source: cartId ? 'checkout' : 'pdp',
      cartId: cartId || '',
      itemId: siteProducts?.[0]?.productId?.toString() || '',
    }),
    [cartId, siteProducts],
  );

  const { data: servicesData } = useApiDataWithDefault<SiteInstallerServices>({
    endpoint: `/installers/${installerId}/services`,
    defaultData: {
      installationPrice: 0,
      installerId: '',
      installerName: '',
      installerProductSubTypes: [],
      installerServiceOfferings: [],
      installerServices: [],
      maxRimSize: '',
      minRimSize: '',
    },
    includeUserRegion: true,
    query,
  });

  const serviceOfferings = useMemo(
    () => servicesData?.siteInstallerServices?.installerServiceOfferings || [],
    [servicesData?.siteInstallerServices?.installerServiceOfferings],
  );

  const searchParams = useSearchParams();
  const urlServiceIds = useMemo(
    () =>
      searchParams?.get('services')
        ? searchParams.get('services')!.split(',').map(Number)
        : [],
    [searchParams],
  );

  const [hasUserSelected, setHasUserSelected] = useState(false);
  const [selectedServiceOfferings, setSelectedServiceOfferings] = useState<
    SelectedServiceOffering[]
  >([]);
  const selectedServiceOfferingsRef = useRef<SelectedServiceOffering[]>([]);
  useEffect(() => {
    selectedServiceOfferingsRef.current = selectedServiceOfferings;
  }, [selectedServiceOfferings]);

  useEffect(() => {
    if (!serviceOfferings.length) {
      return;
    }

    if (isOpen && isSimpleShop && !hasUserSelected) {
      const storedServices = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
      );
      if (storedServices) {
        const parsedServices = JSON.parse(storedServices);
        const mergedServices = parsedServices.map((storedService: any) => {
          const actualService = serviceOfferings.find(
            (s: ServiceOffering) => s.id === storedService.serviceId,
          );
          return actualService
            ? {
                images: actualService.image ? [actualService.image] : [],
                imageUrl: actualService.image,
                notes: storedService.notes || '',
                serviceId: actualService.id,
                serviceName: actualService.name,
              }
            : storedService;
        });
        setSelectedServiceOfferings(mergedServices);
        seStorage.setItem(
          SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
          JSON.stringify(mergedServices),
        );
        return;
      }
      if (cartId) {
        (async () => {
          try {
            const response = await apiGetCartServices({ cartUuid: cartId });
            if (response.isSuccess && response.data) {
              const services = response.data as { services: any[] };
              if (Array.isArray(services.services)) {
                const formattedServices = services.services.map(
                  (service: any) => ({
                    images: service.imagesUrl
                      ? service.imagesUrl.split(',')
                      : [],
                    imageUrl: service.imagesUrl || '',
                    notes: service.notes || '',
                    serviceId: service.serviceId,
                    serviceName: service.serviceName,
                  }),
                );
                setSelectedServiceOfferings(formattedServices);
                seStorage.setItem(
                  SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
                  JSON.stringify(formattedServices),
                );
              }
            }
          } catch (error) {
            logger.error(
              '[InstallationAppointmentSelectionModal] Error fetching services:',
              error,
            );
          }
        })();
        return;
      }
    }

    if (urlServiceIds.length > 0) {
      const matchedServices = serviceOfferings
        .filter((s: ServiceOffering) => urlServiceIds.includes(s.id))
        .map((s: ServiceOffering) => ({
          images: [],
          imageUrl: s.image,
          notes: '',
          serviceId: s.id,
          serviceName: s.name,
        }));

      if (matchedServices.length > 0) {
        // Always update session storage for URL services
        seStorage.setItem(
          SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
          JSON.stringify(matchedServices),
        );

        // Only update local state if user hasn't made selections (prevents flickering)
        if (!hasUserSelected) {
          const currentServicesStr = JSON.stringify(selectedServiceOfferings);
          const newServicesStr = JSON.stringify(matchedServices);
          if (currentServicesStr !== newServicesStr) {
            setSelectedServiceOfferings(matchedServices);
          }
        }
      }
    }

    if (urlServiceIds.length > 0 && installerId && !isOpen) {
      (async () => {
        try {
          const response = await fetch(
            `/api/installers/${installerId}/services?source=pdp&itemId=${siteProducts?.[0]?.productId?.toString() || ''}`,
          );
          if (response.ok) {
            const data = await response.json();
            const offerings =
              data?.siteInstallerServices?.installerServiceOfferings || [];
            if (offerings.length > 0) {
              const matchedServices = offerings
                .filter((s: ServiceOffering) => urlServiceIds.includes(s.id))
                .map((s: ServiceOffering) => ({
                  images: [],
                  imageUrl: s.image,
                  notes: '',
                  serviceId: s.id,
                  serviceName: s.name,
                }));
              if (matchedServices.length > 0) {
                seStorage.setItem(
                  SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
                  JSON.stringify(matchedServices),
                );
              }
            }
          }
        } catch (error) {
          logger.error(
            '[InstallationAppointmentSelectionModal] Error fetching services from URL:',
            error,
          );
        }
      })();
    }
  }, [
    isOpen,
    cartId,
    installerId,
    serviceOfferings,
    isSimpleShop,
    urlServiceIds,
    hasUserSelected,
    selectedServiceOfferings,
    siteProducts,
  ]);

  const handleServiceSelect = (selected: SelectedServiceOffering[]) => {
    setSelectedServiceOfferings(selected);
    setHasUserSelected(true);
    const servicesString = JSON.stringify(selected);
    seStorage.setItem(
      SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
      servicesString,
    );
  };

  const handleClose = () => {
    onClose();
  };

  const [formValue, setFormValue] = useState<
    ShippingConfirmFormProps | undefined
  >({
    selectedTime: selectedDayAndTime,
  });
  const [appointmentTime, setAppointmentTime] = useState<
    SelectedDayAndTime | undefined | null
  >(selectedDayAndTime ?? initialValue?.selectedTime);
  const onError = () => {
    if (!formValue?.selectedTime) {
      setHasError(true);
    }
  };

  useEffect(() => {
    if (formValue?.selectedTime) {
      setHasError(false);
    }
  }, [formValue?.selectedTime]);

  useEffect(() => {
    if (
      isOpen &&
      serviceOfferings.length > 0 &&
      selectedServiceOfferings.length > 0
    ) {
      const storedServices = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
      );

      if (storedServices) {
        const parsedServices = JSON.parse(storedServices);

        const needsMerging = parsedServices.some(
          (service: any) =>
            service.serviceName && service.serviceName.startsWith('Service '),
        );

        if (needsMerging) {
          const mergedServices = parsedServices.map((storedService: any) => {
            const actualService = serviceOfferings.find(
              (s) => s.id === storedService.serviceId,
            );
            return actualService
              ? {
                  images: actualService.image ? [actualService.image] : [],
                  imageUrl: actualService.image,
                  notes: storedService.notes || '',
                  serviceId: actualService.id,
                  serviceName: actualService.name,
                }
              : storedService;
          });

          setSelectedServiceOfferings(mergedServices);

          seStorage.setItem(
            SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
            JSON.stringify(mergedServices),
          );
        }
      }
    }
  }, [isOpen, serviceOfferings, selectedServiceOfferings]);

  useEffect(() => {
    if (urlServiceIds.length > 0 && serviceOfferings.length > 0) {
      const matchedServices = serviceOfferings
        .filter((s) => urlServiceIds.includes(s.id))
        .map((s) => ({
          images: [],
          imageUrl: s.image,
          notes: '',
          serviceId: s.id,
          serviceName: s.name,
        }));

      if (matchedServices.length > 0) {
        seStorage.setItem(
          SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
          JSON.stringify(matchedServices),
        );
      }
    }
  }, [urlServiceIds, serviceOfferings]);

  useEffect(() => {
    if (urlServiceIds.length > 0 && installerId && !isOpen) {
      const fetchServices = async () => {
        try {
          const itemId = siteProducts?.[0]?.productId?.toString() || '';
          const response = await apiGetInstallerServices({
            installerId,
            itemId,
          });

          if (
            response &&
            'isSuccess' in response &&
            response.isSuccess &&
            response.data
          ) {
            const offerings =
              response.data.siteInstallerServices?.installerServiceOfferings ||
              [];
            if (offerings.length > 0) {
              const matchedServices = offerings
                .filter((s: any) => urlServiceIds.includes(s.id))
                .map((s: any) => ({
                  images: [],
                  imageUrl: s.image,
                  notes: '',
                  serviceId: s.id,
                  serviceName: s.name,
                }));

              if (matchedServices.length > 0) {
                seStorage.setItem(
                  SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
                  JSON.stringify(matchedServices),
                );
              }
            }
          }
        } catch (error) {
          logger.error(
            '[InstallationAppointmentSelectionModal] Error fetching services for URL service IDs:',
            error,
          );
        }
      };

      fetchServices();
    }
  }, [urlServiceIds, installerId, isOpen, siteProducts]);

  const handleConfirm = async ({
    siteCartSummaryRequest,
    formValues,
  }: {
    formValues: ShippingConfirmFormValue;
    siteCartSummaryRequest: SiteCartSummaryRequest;
  }) => {
    if (installerId) {
      setSelectedInstaller(installerId);
    }
    setSelectedServices(selectedServiceOfferingsRef.current);
    const servicesPayload = selectedServiceOfferingsRef.current.map((s) => ({
      imagesUrl: s.images ? s.images.join(',') : '',
      notes: s.serviceName.toLowerCase() === 'other' ? s.notes : '',
      serviceId: s.serviceId,
      serviceName: s.serviceName,
    }));

    // Update session storage with selected services
    seStorage.setItem(
      SESSION_STORAGE[PROPERTIES.SELECTED_SERVICES],
      JSON.stringify(selectedServiceOfferingsRef.current),
    );

    try {
      await apiCreateCartServices(cartId, {
        installerId: Number(installerId),
        services: servicesPayload,
      });

      await onConfirm({
        selectedTime: appointmentTime,
        siteCartSummaryRequest,
        formValues,
        installerId,
      });
    } catch (e) {
      logger.error(
        '[InstallationAppointmentSelectionModal] Error in handleConfirm:',
        e,
      );
    }
  };

  const handleScrolling = useCallback(
    (itemRef: RefObject<HTMLDivElement | null>) => {
      scrollToRef(itemRef, TIME.MS400, undefined, modalContentRef.current);
    },
    [],
  );

  const handleSelectTime = (value?: SelectedDayAndTime) => {
    setFormValue((prev) => ({ ...prev, selectedTime: value }));
    setAppointmentTime(value);
  };

  useEffect(() => {
    if (isOpen && !formValue && formValue !== initialValue) {
      setFormValue(initialValue);
    }
  }, [initialValue, isOpen, formValue]);

  useEffect(() => {
    if (!isOpen && formValue) {
      setAppointmentTime(formValue.selectedTime ?? undefined);
    }
  }, [isOpen, formValue]);

  useEffect(() => {
    setFormValue((prev) => ({ ...prev, selectedTime: selectedDayAndTime }));
  }, [selectedDayAndTime]);
  const totalQty =
    siteProducts && siteProducts?.length > 0
      ? calculateTotalNumberOfTiresInCart(siteProducts)
      : 0;

  const total = Number(selectedShop?.price?.salePriceInCents) * totalQty;
  const modalId = isOTS
    ? STATIC_MODAL_IDS.WHATS_INCLUDED_WITH_INSTALLATION_FOR_OTS
    : productSubType === SiteProductSubType.COMMERCIAL
      ? STATIC_MODAL_IDS.WHATS_INCLUDED_WITH_INSTALLATION_FOR_COMMERCIAL_TIRE
      : STATIC_MODAL_IDS.WHATS_INCLUDED_WITH_INSTALLATION;
  const openModal = () => {
    openStaticModal(modalId);
  };
  const isMobileInstall = selectedShop?.isMobileInstall;

  const description = isOTS
    ? ui('checkout.shipping.shippingInfo.installdescriptionOTS')
    : isMobileInstall
      ? ui('checkout.shipping.shippingInfo.mobileInstalldescription')
      : ui('checkout.shipping.shippingInfo.installdescription');
  return (
    <BottomCardModal
      isOpen={isOpen}
      contentLabel="InstallationAppointSelectionModal"
      onClose={handleClose}
      customContentStyles={styles.modal}
      overlayZIndex={Z_INDEX.MODAL - 1}
      contentRef={(node) => (modalContentRef.current = node)}
    >
      {siteInstallerScheduleResponse && (
        <div css={styles.wrapper}>
          <div css={styles.commonSpace}>
            {selectedShop && (
              <ShopNameAddress
                address={selectedShop?.address}
                isMobileInstall={isMobileInstall}
                id={selectedShop?.id}
              />
            )}
          </div>

          <div css={styles.installerHeader}>
            {isOTS
              ? ui('installer.schedule.servicesOTS')
              : ui('installer.schedule.services')}
          </div>
          <div
            css={[
              styles.installerServices,
              isSourcePirelliWidget && styles.installerServicesPirelli,
            ]}
          >
            <span
              css={[
                styles.includedBadge,
                isSourcePirelliWidget && styles.includeBadgePirelli,
              ]}
            >
              <span>{ui('checkout.shipping.shippingInfo.Included')} </span>
              <Icon name={ICONS.REVIEW_VERIFIED} />
            </span>
            {isSimpleShop ? (
              <span css={styles.simpleShopCheckmark} aria-label="Confirmed">
                <svg
                  width="14"
                  height="10"
                  viewBox="0 0 14 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2 5.5L6 9L12 2"
                    stroke="white"
                    strokeWidth="2.2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </span>
            ) : (
              <Icon
                css={[
                  styles.verifiedBadge,
                  isSourcePirelliWidget && styles.verifyBadgePirelli,
                ]}
                name={ICONS.VERIFIED_SMALL}
              />
            )}
            <Icon css={styles.installIcon} name={ICONS.TIRE_INSTALLATION} />
            <div css={styles.tireInstallation}>Tire Installation</div>
            <a css={styles.learnMore} onClick={openModal} role="button">
              {ui('checkout.shipping.shippingInfo.learnMore')}
            </a>
            {selectedShop?.installationCostFor4TiresInCents && (
              <div css={styles.priceContainer}>
                {cartId && (
                  <div>
                    <span css={styles.dollar}>$</span>
                    <span css={styles.totalPrice}>
                      {formatDollars(total).replace('$', '')}
                    </span>
                    <span css={styles.total}>total</span>
                  </div>
                )}
                <div css={styles.pricePerTire}>{`${formatDollars(
                  selectedShop?.price?.salePriceInCents + '',
                )}/tire`}</div>
                <Markdown css={styles.content}>{description}</Markdown>
              </div>
            )}
          </div>

          {serviceOfferings.length > 0 && isSimpleShop && (
            <div>
              <div css={styles.installerHeader}>OTHER SHOP SERVICES</div>
              <div css={styles.servicesSection}>
                <ServiceOfferingsList
                  services={serviceOfferings}
                  onServiceSelect={handleServiceSelect}
                  selectedServices={selectedServiceOfferings}
                />
              </div>
            </div>
          )}

          <div css={styles.installerHeader}>
            {ui('installer.schedule.appointmentDetails')}
          </div>
          <AppointmentDateTimePicker
            scheduleDays={siteInstallerSchedule.scheduleDays}
            onSelect={handleSelectTime}
            value={formValue?.selectedTime}
            defaultSchedule={defaultSchedule}
            error={{
              hasError,
              errorMessage: ui('installer.schedule.error'),
            }}
            isMobileInstall={isMobileInstall}
            isOTS={isOTS}
          />
          <div css={styles.commonSpace}>
            <div
              css={isMobileInstall && styles.formWrapper}
              ref={shippingFormRef}
            >
              <ShippingConfirmForm
                cancelAppointment={
                  initialValue?.selectedTime && cancelAppointment
                }
                selectedTime={formValue?.selectedTime}
                userDetail={userDetail}
                onConfirm={handleConfirm}
                onError={onError}
                email={formValue?.siteCartSummaryRequest?.email || email}
                productSubType={productSubType}
                siteProducts={siteProducts}
                selectedShop={selectedShop}
                handleScroll={handleScrolling}
              />
            </div>
            {!isOTS && (
              <div css={styles.descriptionWrapper}>
                <DescriptionWithIcon
                  icon={
                    isMobileInstall
                      ? ICONS.SHIPPING_TRUCK_OUTLINE
                      : ICONS.LOCAL_BUSINESS
                  }
                  title={
                    isMobileInstall
                      ? ui('installer.info.mobileTitle')
                      : ui('installer.info.title')
                  }
                  description={
                    isMobileInstall
                      ? ui('installer.info.mobileDescription')
                      : ui('installer.info.description')
                  }
                />
              </div>
            )}
          </div>
        </div>
      )}
    </BottomCardModal>
  );
}

export default InstallationAppointmentSelectionModal;
