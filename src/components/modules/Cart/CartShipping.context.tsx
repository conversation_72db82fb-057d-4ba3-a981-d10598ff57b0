/**
 * @NOTE: this context handles appointment, SiteShipping related concerns
 */
import { parseCookies, setCookie } from 'nookies';
import {
  Dispatch,
  memo,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { SelectedDayAndTime } from '~/components/global/InstallationAppointmentSelectionModal/AppointmentDateTimePicker.types';
import {
  SelectedServiceOffering,
  ShippingConfirmFormProps,
} from '~/components/global/InstallationAppointmentSelectionModal/InstallationAppointmentSelectionModal.types';
import { ShippingConfirmFormValue } from '~/components/global/InstallationAppointmentSelectionModal/ShippingConfirmForm/ShippingConfirmForm.types';
import { Shop } from '~/components/global/InstallationShopCard/InstallationShopCard.types';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import { SiteInstallerSourceEnum } from '~/components/modules/PDP/Installers/Installers.types';
import { mapToShipToMeCartShippingRequest } from '~/components/pages/CheckoutPage/checkout.util';
import { BillingInfoValues } from '~/components/pages/CheckoutPage/Payment/BillingInfoForm/BillingInfo.types';
import {
  mapDataToSiteInstallersAndBrandPromotionList,
  PromotionItem,
} from '~/components/pages/ProductDetail/mappers/siteInstallersAndBrandPromotionList';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { SiteCartAppointmentRequest } from '~/data/models/SiteCartAppointmentRequest';
import { SiteCartAppointmentResponse } from '~/data/models/SiteCartAppointmentResponse';
import { SiteCartProductItem } from '~/data/models/SiteCartProductItem';
import { PHONE_TYPE } from '~/data/models/SiteCartShipping';
import { SiteCartShippingRequest } from '~/data/models/SiteCartShippingRequest';
import {
  SHIPPINGSERIVCES,
  ShippingType,
  SiteCartShippingApiResponse,
  SiteCartShippingResponse,
} from '~/data/models/SiteCartShippingResponse';
import {
  PRE_SHIPPING_SELECTION,
  SiteCartSummary,
} from '~/data/models/SiteCartSummary';
import { SiteCartSummaryRequest } from '~/data/models/SiteCartSummaryRequest';
import { SiteInstallerScheduleResponse } from '~/data/models/SiteInstallerSchedule';
import { SiteInstallLocation } from '~/data/models/SiteInstallLocation';
import { SiteShippingLocation } from '~/data/models/SiteShippingLocation';
import { SiteWarehouseShippingLocation } from '~/data/models/SiteWarehouseShippingLocation';
import { useHasChanged } from '~/hooks/useHasChanged';
import useRouteName from '~/hooks/useRouteName';
import useWidgetSource from '~/hooks/useWigetSource';
import {
  apiCreateSiteCartAppointment,
  apiDeleteSiteCartAppointment,
  apiGetSiteCartAppointment,
  apiUpdateSiteCartAppointment,
} from '~/lib/api/checkout/cart-appointment';
import { apiGetSiteCartBilling } from '~/lib/api/checkout/cart-billing';
import {
  apiCreateCartShipping,
  apiDeleteCartShipping,
  apiGetCartShipping,
  apiUpdateCartShipping,
} from '~/lib/api/checkout/cart-shipping';
import {
  apiCreateSiteInstallLocation,
  apiDeleteSiteInstallLocation,
} from '~/lib/api/checkout/install-location';
import { apiGetPickupLocations } from '~/lib/api/checkout/pickup-locations';
import { apiGetWarehousePickupLocations } from '~/lib/api/checkout/warehouse-pickup-locations';
import { apiGetUserDetails } from '~/lib/api/get-user-details';
import { apiGetSiteInstallers } from '~/lib/api/installers';
import { apiGetSiteInstallerSchedule } from '~/lib/api/installers/schedule';
import { COOKIES } from '~/lib/constants/cookies';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { PROPERTIES, SESSION_STORAGE } from '~/lib/constants/sessionStorage';
import { SSOUserIdResponse } from '~/lib/constants/sso.types';
import { AsyncResponse } from '~/lib/fetch/index.types';
import GA from '~/lib/helpers/analytics';
import { getSearchParams } from '~/lib/helpers/app-routes/search-params';
import logger from '~/lib/helpers/logger';
import { seStorage } from '~/lib/utils/browser-storage';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';
import { deleteOldSSOCookie } from '~/lib/utils/sso';

import { cookieConfig } from './CartSummary.constants';
import {
  mapAppointmentToSelectedDayAndTime,
  mapBillingResponseToInfo,
} from './CartSummary.utils';
import { defaultShipToMeRequest } from './CartSummaryModal/ShippingService/shippingService.utils';

interface ContextProps {
  children: ReactNode;
  value?: SiteCartShippingResponse;
}

export interface CartShippingContextProps {
  billingInfo: Partial<BillingInfoValues>;
  brandPromotionList: PromotionItem[] | undefined;
  cartAppointment?: SiteCartAppointmentResponse | null;
  cartInstallLocation?: SiteInstallLocation;
  cartShipping?: SiteCartShippingResponse;
  createCartAppointment: (
    input: SiteCartAppointmentRequest,
    cartSummary?: SiteCartSummary,
    signal?: AbortSignal,
  ) => Promise<SiteCartAppointmentResponse | null | undefined>;
  createCartShipping: (
    input: SiteCartShippingRequest,
    siteCartSummaryRequest?: SiteCartSummaryRequest,
    signal?: AbortSignal,
  ) => void;
  createInstallLocation: (values: ShippingConfirmFormValue) => Promise<void>;
  deleteCartAppointment: () => Promise<void>;
  deleteInstallLocation: () => Promise<void>;
  getBillingInfo: () => Promise<void>;
  getCartAppointment: () => Promise<
    | {
        data: SiteCartAppointmentResponse | 'null';
        isSuccess: true;
        statusCode: number;
      }
    | undefined
  >;
  getCartShipping: (
    forceShipData: boolean,
  ) => Promise<AsyncResponse<SiteCartShippingApiResponse> | undefined>;
  getInstallerSchedule: (
    installerId: string,
    itemId: string,
  ) => Promise<undefined | SiteInstallerScheduleResponse>;
  getPickupLocations: () => void;
  getSSOUserDetails: () => void;
  getSiteInstallers: (
    cartId?: string,
    products?: SiteCartProductItem[],
    isMobileInstall?: boolean,
    hideLoading?: boolean,
  ) => Promise<void>;
  getWarehousePickupLocations: () => void;
  hasUserSelectedInstaller: boolean;
  installerSchedule?: SiteInstallerScheduleResponse;
  isLoading: boolean;
  isLoadingInstallerSchedule: boolean;
  isLocationModalOpen: boolean;
  isSHCostFree: boolean;
  isSHCostTBD: boolean;
  isShopLoading: boolean;
  isWarehouseLoading: boolean;
  pickupLocations: SiteShippingLocation[] | null;
  prevCartProducts: Array<SiteCartProductItem>;
  removeCartShipping: (signal?: AbortSignal) => Promise<void>;
  selectedDayAndTime?: SelectedDayAndTime;
  selectedInstaller: string;
  selectedLocation: string;
  selectedServices: SelectedServiceOffering[];
  setBillingInfo: (values: Partial<BillingInfoValues>) => void;
  setCartAppointment: Dispatch<
    SetStateAction<SiteCartAppointmentResponse | null | undefined>
  >;
  setCartInstallLocation: (value: SiteInstallLocation) => void;
  setCartShipping: Dispatch<
    SetStateAction<SiteCartShippingResponse | undefined>
  >;
  setInstallerSchedule: Dispatch<
    SetStateAction<SiteInstallerScheduleResponse | undefined>
  >;
  setIsLoading: (value: boolean) => void;
  setIsLoadingInstallerSchedule: (value: boolean) => void;
  setIsLocationModalOpen: (isLocationModalOpen: boolean) => void;
  setIsShopLoading: (value: boolean) => void;
  setIsWarehouseLoading: (value: boolean) => void;
  setPrevCartProducts: (value: Array<SiteCartProductItem>) => void;
  setSelectedDayAndTime: (value: SelectedDayAndTime) => void;
  setSelectedInstaller: (value: string) => void;
  setSelectedLocation: (id: string) => void;
  setSelectedServices: Dispatch<SetStateAction<SelectedServiceOffering[]>>;
  setShippingForm: Dispatch<
    SetStateAction<ShippingConfirmFormProps | undefined>
  >;
  setWarehouseSelectedLocation: (id: string) => void;
  shippingForm: ShippingConfirmFormProps | undefined;
  siteInstallers?: Array<Shop>;
  ssoUserDetails: SSOUserIdResponse | null;
  updateCartAppointment: (
    input: SiteCartAppointmentRequest,
    signal?: AbortSignal,
  ) => Promise<SiteCartAppointmentResponse | null | undefined>;
  updateCartShipping: (
    input: SiteCartShippingRequest,
    siteCartSummaryRequest?: SiteCartSummaryRequest,
    signal?: AbortSignal,
  ) => Promise<AsyncResponse<SiteCartShippingApiResponse> | undefined>;
  updateUserSelectedInstaller: (value: boolean) => void;
  verifiedCount: number;
  warehousePickupLocations: SiteWarehouseShippingLocation[] | null;
  warehouseSelectedLocation: string;
}

const CartShippingContext = createContext<CartShippingContextProps>();

export const initialBillingInfo: BillingInfoValues = {
  addressLine1: '',
  addressLine2: '',
  city: '',
  email: '',
  firstName: '',
  lastName: '',
  phone: '',
  phoneType: PHONE_TYPE.MOBILE,
  poNumber: '',
  state: '',
  zip: '',
};

function useContextSetup(
  cartShippingSSR?: SiteCartShippingResponse,
): CartShippingContextProps {
  const {
    cartId,
    cartSummary,
    setHasDefaultSelectMobileInstall,
    updateCartSummary,
    setHasMobileInstall,
  } = useCartSummaryContextSelector((v) => ({
    cartId: v.cartId,
    cartSummary: v.cartSummary,
    setHasDefaultSelectMobileInstall: v.setHasDefaultSelectMobileInstall,
    setHasMobileInstall: v.setHasMobileInstall,
    updateCartSummary: v.updateCartSummary,
  }));

  const { isComingFromWidget } = useWidgetSource();

  const [cartShipping, setCartShipping] = useState<
    SiteCartShippingResponse | undefined
  >(cartShippingSSR);
  const [isSHCostFree, setIsSHCostFree] = useState<boolean>(false);
  const [isSHCostTBD, setIsSHCostTBD] = useState<boolean>(true);
  const [selectedInstaller, setSelectedInstaller] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [warehouseSelectedLocation, setWarehouseSelectedLocation] =
    useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [siteInstallers, setSiteInstallers] = useState<Shop[]>();
  const [verifiedCount, setVerifiedCount] = useState(0);
  const [brandPromotionList, setBrandPromotionList] =
    useState<PromotionItem[]>();
  const [isLoadingInstallerSchedule, setIsLoadingInstallerSchedule] =
    useState(false);
  const [pickupLocations, setPickupLocations] = useState<
    SiteShippingLocation[] | null
  >(null);
  const [warehousePickupLocations, setWarehousePickupLocations] = useState<
    SiteWarehouseShippingLocation[] | null
  >(null);
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false);
  const [cartAppointment, setCartAppointment] = useState<
    SiteCartAppointmentResponse | null | undefined
  >();
  const [installerSchedule, setInstallerSchedule] =
    useState<SiteInstallerScheduleResponse>();
  const [hasUserSelectedInstaller, updateUserSelectedInstaller] =
    useState<boolean>(false);
  const [billingInfo, setBillingInfo] =
    useState<Partial<BillingInfoValues>>(initialBillingInfo);

  const [shippingForm, setShippingForm] = useState<
    ShippingConfirmFormProps | undefined
  >();
  const [isShopLoading, setIsShopLoading] = useState(false);
  const [isWarehouseLoading, setIsWarehouseLoading] = useState(false);
  const [cartInstallLocation, setCartInstallLocation] = useState<
    SiteInstallLocation | undefined
  >();

  const cookies = parseCookies();
  const storedCartId = cookies[COOKIES.CART_ID] || cartId || null;
  const isAppointmentDataSet = cookies[COOKIES.CART_APPOINTMENT] || null;
  const isBillingInfoSet = cookies[COOKIES.BILLING_PAYMENT] || null;

  const [ssoUserDetails, setUserDetailsSSO] =
    useState<SSOUserIdResponse | null>(null);
  const [selectedDayAndTime, setSelectedDayAndTime] =
    useState<SelectedDayAndTime>();
  const [prevCartProducts, setPrevCartProducts] = useState<
    Array<SiteCartProductItem>
  >([]);
  const selectedInstallerId = seStorage.getItem(
    SESSION_STORAGE.INSTALLER_ID_SELECTED,
  );

  // duplication with ProductDetailContext.
  // hooks for setting initial selected date in appointment time picker
  const scheduleDays = useMemo(
    () => installerSchedule?.siteInstallerSchedule.scheduleDays || [],
    [installerSchedule?.siteInstallerSchedule.scheduleDays],
  );

  const selectedInstallerIds = seStorage.getItem(SESSION_STORAGE.INSTALLER_IDS);

  const [selectedServices, setSelectedServices] = useState<
    SelectedServiceOffering[]
  >([]);

  const getSSOUserDetails = useCallback(async () => {
    const response = await apiGetUserDetails();
    if (response.isSuccess) {
      if (response.data) {
        setUserDetailsSSO(response.data);
      }
    } else {
      throw Error('something went wrong with getting user details.');
    }
  }, []);

  const getCartAppointment = useCallback(async () => {
    if (!storedCartId) {
      return;
    }

    const response = await apiGetSiteCartAppointment({
      query: { id: storedCartId },
    });

    if (response.isSuccess) {
      setCookie(null, COOKIES.CART_APPOINTMENT, '1', cookieConfig);
      if (response.data === 'null') {
        setCartAppointment(null);
        return;
      }

      setCartAppointment(response.data);
      return response;
    } else {
      setCookie(null, COOKIES.CART_APPOINTMENT, '', cookieConfig);
      logger.error(response.error.message);
      return;
    }
  }, [storedCartId]);

  const createCartShipping = useCallback(
    async (
      cartShippingRequest: SiteCartShippingRequest,
      siteCartSummaryRequest?: SiteCartSummaryRequest,
      signal?: AbortSignal,
    ) => {
      if (
        !storedCartId ||
        (cartShippingRequest.shippingOption !== ShippingType.HOME &&
          !cartShippingRequest.addressLine1)
      ) {
        return;
      }
      setIsLoading(true);

      const response = await apiCreateCartShipping({
        includeUserRegion: true,
        includeUserZip: true,
        input: cartShippingRequest,
        query: { cartId: storedCartId },
        signal,
      });

      const updateCartSummaryWithOption = async () => {
        if (cartShippingRequest.shippingOption === ShippingType.INSTALLER) {
          await updateCartSummary(
            {
              email: cartShippingRequest.email,
              excludeShipping: false,
              vehicleDescription: siteCartSummaryRequest?.vehicleDescription,
              vehicleMake: siteCartSummaryRequest?.vehicleMake,
              vehicleModel: siteCartSummaryRequest?.vehicleModel,
              vehicleTrim: siteCartSummaryRequest?.vehicleTrim,
              vehicleYear: siteCartSummaryRequest?.vehicleYear,
            },
            signal,
          );
        } else {
          let preShippingSelection: PRE_SHIPPING_SELECTION =
            PRE_SHIPPING_SELECTION.SHIP_TO_SHOP;

          if (cartShippingRequest.shippingOption === ShippingType.HOME) {
            preShippingSelection = PRE_SHIPPING_SELECTION.SHIP_TO_ME;
          } else if (
            cartShippingRequest.shippingOption === ShippingType.FEDEX
          ) {
            preShippingSelection = PRE_SHIPPING_SELECTION.SHIP_TO_FEDEX;
          }

          await updateCartSummary(
            {
              email: cartShippingRequest.email,
              excludeShipping: false,
              preShippingSelection,
            },
            signal,
          );
        }
      };

      if (response.isSuccess) {
        setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);
        await updateCartSummaryWithOption();
        setCartShipping(response.data.siteCartShippingResponse);
      } else {
        const updatedResponse = await apiUpdateCartShipping({
          includeUserRegion: true,
          includeUserZip: true,
          input: cartShippingRequest,
          query: { cartId: storedCartId },
          signal,
        });

        if (updatedResponse.isSuccess) {
          setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);

          await updateCartSummaryWithOption();
          setCartShipping(updatedResponse.data.siteCartShippingResponse);
        }
      }
      setIsLoading(false);
    },
    [storedCartId, updateCartSummary],
  );

  const prevInstallerRef = useRef('');

  const updateCartShipping = useCallback(
    async (
      cartShippingRequest: SiteCartShippingRequest,
      siteCartSummaryRequest?: SiteCartSummaryRequest,
      signal?: AbortSignal,
    ) => {
      if (
        !storedCartId ||
        (cartShippingRequest.shippingOption !== ShippingType.HOME &&
          cartShippingRequest.shippingOption !== ShippingType.WAREHOUSE &&
          !cartShippingRequest.addressLine1)
      ) {
        return;
      }
      setIsLoading(true);
      try {
        const response = await apiUpdateCartShipping({
          includeUserRegion: true,
          includeUserZip: true,
          input: cartShippingRequest,
          query: { cartId: storedCartId },
          signal,
        });

        const updateCartSummaryWithOption = async () => {
          if (cartShippingRequest.shippingOption === ShippingType.INSTALLER) {
            // Break the loop
            if (prevInstallerRef.current === cartShippingRequest.installerId) {
              return;
            } else {
              prevInstallerRef.current = cartShippingRequest.installerId ?? '';
            }

            await updateCartSummary(
              {
                email: cartShippingRequest.email,
                excludeShipping: false,
                vehicleDescription: siteCartSummaryRequest?.vehicleDescription,
                vehicleMake: siteCartSummaryRequest?.vehicleMake,
                vehicleModel: siteCartSummaryRequest?.vehicleModel,
                vehicleTrim: siteCartSummaryRequest?.vehicleTrim,
                vehicleYear: siteCartSummaryRequest?.vehicleYear,
              },
              signal,
            );
          } else {
            await updateCartSummary(
              {
                email: cartShippingRequest.email,
                excludeShipping: false,
                installerId: null,
                removeInstallerId: cartSummary?.installerDetails?.installerId,
              },
              signal,
            );
          }
        };
        if (response.isSuccess) {
          setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);
          setCartShipping(response.data.siteCartShippingResponse);
          await updateCartSummaryWithOption();
          setIsLoading(false);
        } else {
          const createdResponse = await apiCreateCartShipping({
            includeUserRegion: true,
            includeUserZip: true,
            input: cartShippingRequest,
            query: { cartId: storedCartId },
            signal,
          });

          if (createdResponse.isSuccess) {
            await updateCartSummaryWithOption();
          }
        }
        return response;
      } finally {
        setIsLoading(false);
      }
    },
    [
      storedCartId,
      updateCartSummary,
      cartSummary?.installerDetails?.installerId,
    ],
  );

  const updateCartAppointment = useCallback(
    async (
      cartAppointmentRequest: SiteCartAppointmentRequest,
      signal?: AbortSignal,
    ) => {
      if (!cartId) {
        return;
      }

      const response = await apiUpdateSiteCartAppointment({
        body: cartAppointmentRequest,
        query: { id: cartId },
        signal,
      });

      if (response.isSuccess) {
        setCookie(null, COOKIES.CART_APPOINTMENT, '1', cookieConfig);
        if (response.data === 'null') {
          setCartAppointment(null);
          return;
        }
        setCartAppointment(response.data);
        return response.data;
      } else if (response.error.statusCode === 404) {
        setCookie(null, COOKIES.CART_APPOINTMENT, '', cookieConfig);
        const res = await apiCreateSiteCartAppointment({
          body: cartAppointmentRequest,
          query: { id: cartId },
          signal,
        });

        if (res.isSuccess) {
          setCookie(null, COOKIES.CART_APPOINTMENT, '1', cookieConfig);
          if (res.data === 'null') {
            setCartAppointment(null);
            return;
          }
          setCartAppointment(res.data);
          return res.data;
        } else {
          setCookie(null, COOKIES.CART_APPOINTMENT, '', cookieConfig);
          return;
        }
      } else {
        setCookie(null, COOKIES.CART_APPOINTMENT, '', cookieConfig);
        logger.info(response.error.message);
        return;
      }
    },
    [cartId],
  );

  const getCartShipping = useCallback(
    async (
      forceShipData = false,
    ): Promise<AsyncResponse<SiteCartShippingApiResponse> | undefined> => {
      if (!storedCartId || !forceShipData) {
        return;
      }

      setIsLoading(true);
      const response = await apiGetCartShipping({
        query: { cartId: storedCartId },
      });

      if (response.isSuccess) {
        setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);
        setCartShipping(response.data.siteCartShippingResponse);
        setIsLoading(false);
        return response;
      } else if (response.error.statusCode === 404) {
        setCookie(null, COOKIES.CART_SHIPPING, '', cookieConfig);
        setCartShipping(undefined);
      } else {
        logger.error('Error in fetching cart shipping');
      }
      setIsLoading(false);
      return;
    },
    [storedCartId],
  );

  const removeCartShipping = useCallback(
    async (signal?: AbortSignal) => {
      if (!storedCartId) {
        return;
      }

      const cartShippingRes = await apiDeleteCartShipping({
        query: { cartId: storedCartId },
        signal,
      });

      if (
        cartShippingRes.isSuccess ||
        cartShippingRes.error.statusCode === 404
      ) {
        setCookie(null, COOKIES.CART_SHIPPING, '', cookieConfig);
        setCartShipping(undefined);
      }
    },
    [storedCartId],
  );

  const getPickupLocations = useCallback(async () => {
    const searchParams = getSearchParams();
    const widgetSource = searchParams?.get('widgetSource');
    if (widgetSource === 'pirelli') {
      return;
    }
    const response = await apiGetPickupLocations({
      includeUserRegion: true,
      includeUserZip: true,
      limit: '',
    });

    if (response.isSuccess) {
      setPickupLocations(response.data.locations);
    } else {
      throw new Error(response.error.message);
    }
  }, []);

  const getWarehousePickupLocations = useCallback(async () => {
    const productIds =
      cartSummary?.siteProducts.reduce((prev, cur, index) => {
        return index === cartSummary?.siteProducts?.length - 1
          ? prev + cur.productId + ''
          : prev + cur.productId + ',';
      }, '') || '';

    if (productIds) {
      setIsWarehouseLoading(true);
      const response = await apiGetWarehousePickupLocations({
        includeUserRegion: true,
        includeUserZip: true,
        productIds,
      });
      if (response.isSuccess) {
        setWarehousePickupLocations(
          response.data.siteWarehouses.siteWarehouseList,
        );
        setIsWarehouseLoading(false);
      } else {
        setIsWarehouseLoading(false);
        throw new Error(response.error.message);
      }
    }
  }, [cartSummary?.siteProducts]);

  const getSiteInstallers = useCallback(
    async (
      cartId?: string,
      products?: SiteCartProductItem[],
      isMobileInstall?: boolean,
      isNoLimit?: boolean,
      hideLoading?: boolean,
    ) => {
      if (!cartId || !products || products?.length === 0) {
        return;
      }
      if (!hideLoading) {
        setIsShopLoading(true);
      }
      const searchParams = getSearchParams();
      const widgetType = searchParams?.get('widgetType');
      const hasMobileInstallInSearchParams =
        searchParams?.get('hasMobileInstall');
      const activeShippingTypeInSearchParams =
        searchParams?.get('activeShippingType');

      const query: any = {
        cartId: cartId !== '400' ? cartId : '',
        frontQuantity:
          products.reduce((prev, cur) => {
            return prev + cur.quantity;
          }, 0) + '',
        installerIds:
          widgetType === 'installer-widget' ? '' : selectedInstallerIds || '',
        itemId: products[0].productId.toString(),
        limit: '4',
        mobileInstall: !!isMobileInstall + '',
        source: SiteInstallerSourceEnum.CHECKOUT,
        userZip: cartSummary?.zip ? cartSummary?.zip : '',
      };

      if (isNoLimit) {
        delete query.limit;
      }

      const response = await apiGetSiteInstallers({
        includeUserTime: true,
        query,
      });

      const hasDefaultSelectMobileInstallFromWidget =
        hasMobileInstallInSearchParams &&
        activeShippingTypeInSearchParams === SHIPPINGSERIVCES.MOBILEINSTALL;

      if (response.isSuccess) {
        const mapData = mapDataToSiteInstallersAndBrandPromotionList(
          response.data,
        );
        setSiteInstallers(() => mapData.siteInstallers);
        setVerifiedCount(
          response.data.verifiedCount ? response.data.verifiedCount : 0,
        );
        setBrandPromotionList(mapData.brandPromotionList);
        if (isMobileInstall) {
          if (
            isComingFromWidget &&
            hasDefaultSelectMobileInstallFromWidget &&
            response.data.defaultSelectMobileInstall
          ) {
            setHasDefaultSelectMobileInstall(true);
          } else if (
            !isComingFromWidget &&
            response.data.defaultSelectMobileInstall
          ) {
            setHasDefaultSelectMobileInstall(true);
          } else {
            setHasDefaultSelectMobileInstall(false);
          }
        }
        // need to separate mobile installers and normal installers to reduce the complexity
        if (
          response.data.siteInstallers.siteInstallerList.length > 0 &&
          response.data.siteInstallers.siteInstallerList[0].isMobileInstall
        ) {
          setHasMobileInstall(true);
        } else if (
          response.data.siteInstallers.siteInstallerList.length === 0 &&
          isMobileInstall
        ) {
          setHasMobileInstall(false);
        }
        setIsShopLoading(false);
      } else {
        setIsShopLoading(false);
        throw new Error(response.error.message);
      }
    },
    [
      selectedInstallerIds,
      cartSummary?.zip,
      isComingFromWidget,
      setHasDefaultSelectMobileInstall,
      setHasMobileInstall,
    ],
  );

  const getInstallerSchedule = useCallback(
    async (
      installerId: string,
      itemId: string,
    ): Promise<undefined | SiteInstallerScheduleResponse> => {
      if (!installerId) {
        return;
      }
      const result = await apiGetSiteInstallerSchedule({
        includeUserTime: true,
        installerId,
        query: {
          source: storedCartId
            ? SiteInstallerSourceEnum.CHECKOUT
            : SiteInstallerSourceEnum.PDP,
          cartId: storedCartId || '',
          itemId,
        },
      });

      if (result.isSuccess) {
        setInstallerSchedule(result.data);
        return result.data;
      } else {
        setIsLoadingInstallerSchedule(false);
        throw new Error(result.error.message);
      }
    },
    [storedCartId],
  );

  const createCartAppointment = useCallback(
    async (
      input: SiteCartAppointmentRequest,
      cartSummary?: SiteCartSummary,
      signal?: AbortSignal,
    ) => {
      if (!cartId) {
        logger.error('There is no cart yet!');
        return;
      }

      const res = await apiCreateSiteCartAppointment({
        body: input,
        query: {
          id: cartId,
        },
        signal,
      });

      if (res.isSuccess) {
        setCookie(null, COOKIES.CART_APPOINTMENT, '1', cookieConfig);
        GA.addToDataLayer({
          event: 'checkout',
          ecommerce: {
            checkout: {
              actionField: {
                step: 1,
                option: 'ScheduleInstallation:ShipInstaller',
              },
              products: cartSummary?.siteProducts,
            },
          },
        });
        if (res.data === 'null') {
          setCartAppointment(null);
          return;
        }
        setCartAppointment(res.data);
        return res.data;
      } else {
        const response = await apiUpdateSiteCartAppointment({
          body: input,
          query: {
            id: cartId,
          },
          signal,
        });

        if (response.isSuccess) {
          setCookie(null, COOKIES.CART_APPOINTMENT, '1', cookieConfig);
          GA.addToDataLayer({
            event: 'checkout',
            ecommerce: {
              checkout: {
                actionField: {
                  step: 1,
                  option: 'ScheduleInstallation:ShipInstaller',
                },
                products: cartSummary?.siteProducts,
              },
            },
          });
          if (response.data === 'null') {
            setCartAppointment(null);
            return;
          }
          setCartAppointment(response.data);
          return response.data;
        }

        setCookie(null, COOKIES.CART_APPOINTMENT, '', cookieConfig);
        throw new Error(res.error.message);
      }
    },
    [cartId],
  );

  const deleteCartAppointment = useCallback(async () => {
    if (!cartId) {
      return;
    }

    const res = await apiDeleteSiteCartAppointment({
      query: {
        id: cartId,
      },
    });

    if (res.isSuccess || res.error.statusCode === 404) {
      setCookie(null, COOKIES.CART_APPOINTMENT, '', cookieConfig);
      setCartAppointment(undefined);
    }
  }, [cartId]);

  const getBillingInfo = useCallback(async (): Promise<void> => {
    if (!cartId) {
      return;
    }

    const cartBillingResponse = await apiGetSiteCartBilling({
      query: { cartId },
    });

    if (cartBillingResponse.isSuccess) {
      setBillingInfo(
        mapBillingResponseToInfo(
          cartBillingResponse.data.siteCartBillingResponse,
        ),
      );
    } else {
      throw new Error(cartBillingResponse.error.message);
    }
  }, [cartId]);

  const createInstallLocation = useCallback(
    async (formValues: ShippingConfirmFormValue) => {
      const response = await apiCreateSiteInstallLocation({
        input: formValues,
        query: { cartId: storedCartId || '' },
      });

      if (response.isSuccess) {
        setCartInstallLocation(
          response.data.siteCartInstallLocationResponse.cartInstallLocation,
        );
      }
    },
    [storedCartId],
  );

  const deleteInstallLocation = useCallback(async () => {
    const response = await apiDeleteSiteInstallLocation({
      query: { cartId: storedCartId || '' },
    });

    if (response.isSuccess) {
      setCartInstallLocation(undefined);
    }
  }, [storedCartId]);

  const getInitialCartShippingForWidget = useCallback(async () => {
    if (isComingFromWidget) {
      if (cartShipping) {
        return;
      }
      const searchParams = getSearchParams();
      const activeShippingTypeInSearchParams =
        searchParams?.get('activeShippingType');

      if (activeShippingTypeInSearchParams === SHIPPINGSERIVCES.SHIPTOME) {
        const input = mapToShipToMeCartShippingRequest(
          defaultShipToMeRequest('', '', ''),
        );
        await createCartShipping(input);
      }
    }
  }, [cartShipping, createCartShipping, isComingFromWidget]);

  useEffect(() => {
    if (cartShippingSSR) {
      setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);
    } else {
      setCookie(null, COOKIES.CART_SHIPPING, '', cookieConfig);
    }
  }, [cartShippingSSR]);

  useEffect(() => {
    if (!isAppointmentDataSet && !isComingFromWidget) {
      return;
    }

    getCartAppointment();
  }, [getCartAppointment, isAppointmentDataSet, isComingFromWidget]);

  useEffect(() => {
    if (!storedCartId || !isBillingInfoSet) {
      return;
    }

    getBillingInfo();
  }, [getBillingInfo, isBillingInfoSet, storedCartId]);

  useEffect(() => {
    if (!cartSummary) {
      return;
    }
    if (selectedInstallerId) {
      // STHD-8456 a trick to make sure selected installer of current cart is selected in Cart summary panel after refreshing payments page
      setTimeout(() => {
        setSelectedInstaller(selectedInstallerId);
      }, 0);
      return;
    }

    if (!cartSummary.installerDetails) {
      setSelectedInstaller('');
      return;
    }
    setSelectedInstaller(cartSummary.installerDetails.installerId);
  }, [cartSummary, selectedInstallerId]);

  useEffect(() => {
    if (!installerSchedule || !scheduleDays.length) {
      return;
    }

    if (cartAppointment === null) {
      setShippingForm((prev) => {
        // if shippingForm is undefined, which is initial state. don't need to overwrite selectedTime value since it causes tons of unexpected re-render in multiple places.
        if (!prev) {
          return prev;
        }
        return { ...prev, selectedTime: null };
      });
      return;
    }

    if (typeof cartAppointment === 'undefined') {
      setShippingForm((prev) => {
        // if shippingForm is undefined, which is initial state. don't need to overwrite selectedTime value since it causes tons of unexpected re-render in multiple places.
        if (!prev) {
          return prev;
        }
        return { ...prev, selectedTime: undefined };
      });
      return;
    }

    if (
      cartAppointment &&
      cartAppointment.installer?.installerId === selectedInstaller
    ) {
      const selectedTime = mapAppointmentToSelectedDayAndTime(cartAppointment);
      setShippingForm((prev) => ({ ...prev, selectedTime }));
    }
  }, [scheduleDays, cartAppointment, installerSchedule, selectedInstaller]);

  useEffect(() => {
    getInitialCartShippingForWidget();
  }, [getInitialCartShippingForWidget]);

  useEffect(() => {
    if (!cartSummary) {
      return;
    }
    const {
      siteProducts,
      coreShipHandlingMode,
      shippingCostToHomeInCents,
      shippingCostInCents,
      coreShipHandlingFeesApplied,
    } = cartSummary;
    const isAllInstallable = siteProducts.every(
      (product) => !!product.isInstallable,
    );
    const hasOversized = siteProducts.some((product) => product.oversized);
    const hasUninstallable = siteProducts.some(
      (product) => !product.isInstallable,
    );

    setIsSHCostTBD(
      (coreShipHandlingMode === 2 &&
        isAllInstallable &&
        !cartShipping?.cartShipping) ||
        (coreShipHandlingMode === 3 &&
          !isAllInstallable &&
          !cartShipping?.cartShipping) ||
        (coreShipHandlingMode === 4 &&
          isAllInstallable &&
          !cartShipping?.cartShipping),
    );

    const isShippingFree = !coreShipHandlingFeesApplied && !shippingCostInCents;
    const searchParams = getSearchParams();
    const activeShippingTypeInSearchParams =
      searchParams?.get('activeShippingType');
    const cartShippingOption = isComingFromWidget
      ? activeShippingTypeInSearchParams !== SHIPPINGSERIVCES.SHIPTOME
      : cartShipping?.shippingOption !== ShippingType.HOME;

    setIsSHCostFree(
      coreShipHandlingMode === 1 ||
        (coreShipHandlingMode === 2 &&
          ((isAllInstallable && cartShippingOption) || hasUninstallable)) ||
        (coreShipHandlingMode === 3 &&
          ((isAllInstallable && !hasOversized && cartShippingOption) ||
            (hasUninstallable &&
              !hasOversized &&
              !shippingCostToHomeInCents))) ||
        (coreShipHandlingMode === 4 && !hasOversized) ||
        isShippingFree,
    );
  }, [cartSummary, cartShipping, isComingFromWidget]);

  return {
    billingInfo,
    brandPromotionList,
    cartAppointment,
    cartInstallLocation,
    cartShipping,
    createCartAppointment,
    createCartShipping,
    createInstallLocation,
    deleteCartAppointment,
    deleteInstallLocation,
    getBillingInfo,
    getCartAppointment,
    getCartShipping,
    getInstallerSchedule,
    getPickupLocations,
    getSiteInstallers,
    getSSOUserDetails,
    getWarehousePickupLocations,
    hasUserSelectedInstaller,
    installerSchedule,
    isLoading,
    isLoadingInstallerSchedule,
    isLocationModalOpen,
    isSHCostFree,
    isSHCostTBD,
    isShopLoading,
    isWarehouseLoading,
    pickupLocations,
    prevCartProducts,
    removeCartShipping,
    selectedDayAndTime,
    selectedInstaller,
    selectedLocation,
    selectedServices,
    setBillingInfo,
    setCartAppointment,
    setCartInstallLocation,
    setCartShipping,
    setInstallerSchedule,
    setIsLoading,
    setIsLoadingInstallerSchedule,
    setIsLocationModalOpen,
    setIsShopLoading,
    setIsWarehouseLoading,
    setPrevCartProducts,
    setSelectedDayAndTime,
    setSelectedInstaller,
    setSelectedLocation,
    setSelectedServices,
    setShippingForm,
    setWarehouseSelectedLocation,
    shippingForm,
    siteInstallers,
    ssoUserDetails,
    updateCartAppointment,
    updateCartShipping,
    updateUserSelectedInstaller,
    verifiedCount,
    warehousePickupLocations,
    warehouseSelectedLocation,
  };
}

function CartShippingContextGetInstallerScheduleEffect() {
  const { siteInstallersFirstId, getInstallerSchedule } =
    useCartShippingContextSelector((v) => ({
      siteInstallersFirstId: v?.siteInstallers?.[0]?.id ?? null,
      getInstallerSchedule: v.getInstallerSchedule,
    }));
  const { cartSummaryFirstProduct, cartSummaryInstallerId } =
    useCartSummaryContextSelector((v) => ({
      cartSummaryFirstProduct: v.cartSummary?.siteProducts?.[0] ?? null,
      cartSummaryInstallerId:
        v.cartSummary?.installerDetails?.installerId ?? null,
    }));
  const newUserLocation = useUserPersonalizationContextSelector(
    (v) => v.userPersonalizationData?.userLocation?.cityName || null,
  );

  const routeName = useRouteName();
  const installerId = cartSummaryInstallerId || siteInstallersFirstId;
  const isPDPPage =
    routeName === ROUTE_MAP[ROUTES.PRODUCT_DETAIL] ||
    routeName === ROUTE_MAP[ROUTES.PRODUCT_DETAIL_PLA];

  useEffect(() => {
    if (!installerId || !newUserLocation || !cartSummaryFirstProduct) {
      return;
    }

    // need to replace with specific installer id because it has not been selected yet.
    (async () => {
      await getInstallerSchedule(
        installerId,
        cartSummaryFirstProduct.productId + '',
      );
    })();
  }, [
    getInstallerSchedule,
    newUserLocation,
    isPDPPage,
    installerId,
    cartSummaryFirstProduct,
  ]);

  return null;
}

function CartShippingContextWarehousePickupEffect() {
  const cartSummaryFirstProduct = useCartSummaryContextSelector(
    (v) => v.cartSummary?.siteProducts?.[0] ?? null,
  );

  const getWarehousePickupLocations = useCartShippingContextSelector(
    (v) => v.getWarehousePickupLocations,
  );
  const newUserLocation = useUserPersonalizationContextSelector(
    (v) => v.userPersonalizationData?.userLocation?.cityName || null,
  );

  const zipChanged = useHasChanged(newUserLocation, true);
  const cookies = parseCookies();
  const storedZipCode = cookies[COOKIES.ZIPCODE] || null;
  const storedCartId = cookies[COOKIES.CART_ID] || null;

  useEffect(() => {
    if (!storedCartId) {
      return;
    }

    if (!storedZipCode && !newUserLocation && !cartSummaryFirstProduct) {
      return;
    }

    getWarehousePickupLocations();
  }, [
    cartSummaryFirstProduct,
    getWarehousePickupLocations,
    newUserLocation,
    storedCartId,
    storedZipCode,
    zipChanged,
  ]);

  return null;
}

function CartShippingContextInstallersEffect() {
  const { isComingFromWidget } = useWidgetSource();

  const { getSiteInstallers, getPickupLocations } =
    useCartShippingContextSelector((v) => ({
      getSiteInstallers: v.getSiteInstallers,
      getPickupLocations: v.getPickupLocations,
    }));
  const {
    cartSummary,
    cartId,
    hasDefaultSelectMobileInstall,
    setCartSummaryApiLoading,
    setHasDefaultSelectMobileInstall,
    setHasMobileInstall,
  } = useCartSummaryContextSelector((v) => ({
    cartId: v.cartId,
    cartSummary: v.cartSummary,
    hasDefaultSelectMobileInstall: v.hasDefaultSelectMobileInstall,
    setCartSummaryApiLoading: v.setIsLoading,
    setHasDefaultSelectMobileInstall: v.setHasDefaultSelectMobileInstall,
    setHasMobileInstall: v.setHasMobileInstall,
  }));
  const { newUserLocation, zip, region } =
    useUserPersonalizationContextSelector((v) => ({
      newUserLocation:
        v.userPersonalizationData?.userLocation?.cityName || null,
      zip: v.userPersonalizationData?.userLocation?.zip || '',
      region: v.userPersonalizationData?.userLocation?.region || '',
    }));
  const cartProductsLength = cartSummary?.siteProducts.length;
  const zipChanged = useHasChanged(newUserLocation, true);
  const cookies = parseCookies();
  const storedZipCode = cookies[COOKIES.ZIPCODE] || null;
  const storedCartId = cookies[COOKIES.CART_ID] || null;

  const selectedInstallerIds = seStorage.getItem(SESSION_STORAGE.INSTALLER_IDS);
  const selectedInstallerId = seStorage.getItem(
    SESSION_STORAGE.INSTALLER_ID_SELECTED,
  );
  const isSelectedInstallerFromWidgetConfig =
    selectedInstallerId && selectedInstallerIds?.includes(selectedInstallerId);
  const isMobileInstallerFromWidget = Boolean(
    seStorage.getItem(SESSION_STORAGE.IS_MOBILE_INSTALL),
  );
  const isSimpleShop = useGlobalsContextSelector(
    (v) => Number(v.isSimpleShop) === 1,
  );

  useEffect(() => {
    (async () => {
      const searchParams = getSearchParams();
      const hasMobileInstallInSearchParams =
        searchParams?.get('hasMobileInstall');
      const activeShippingTypeInSearchParams =
        searchParams?.get('activeShippingType');
      const hasDefaultSelectMobileInstallFromWidget =
        hasMobileInstallInSearchParams &&
        activeShippingTypeInSearchParams === SHIPPINGSERIVCES.MOBILEINSTALL;

      await deleteOldSSOCookie(); // remove this after a month - it is just to remove unwanted cookies

      if (
        newUserLocation &&
        storedZipCode &&
        storedZipCode !== newUserLocation
      ) {
        setCookie(null, COOKIES.ZIPCODE, newUserLocation, cookieConfig);
        setCookie(null, COOKIES.ZIP, zip, cookieConfig);
        setCookie(null, COOKIES.REGION, String(region), cookieConfig);
      } else if (newUserLocation) {
        setCookie(null, COOKIES.ZIPCODE, newUserLocation, cookieConfig);
        setCookie(null, COOKIES.ZIP, zip, cookieConfig);
        setCookie(null, COOKIES.REGION, String(region), cookieConfig);
      }

      if (!storedZipCode && !newUserLocation) {
        return;
      }

      // avoid whole CartSummary context is updated when an anonymous user is visiting landing page.
      if ((zipChanged && cartProductsLength) || storedCartId) {
        setCartSummaryApiLoading(true);
      }
      const preSelectedInstallerTypeIsMobile = seStorage.getItem(
        SESSION_STORAGE[PROPERTIES.PRE_SELECTED_INSTALLER_TYPE],
      );

      try {
        // checking if mobile install is available when the zipcode changes and cart is not empty.
        if (zipChanged && cartProductsLength) {
          if (!isSimpleShop) {
            // No need to check for mobile install as we get the value from widget app config
            const response = await apiGetSiteInstallers({
              includeUserTime: true,
              query: {
                cartId,
                frontQuantity:
                  cartSummary?.siteProducts.reduce((prev, cur) => {
                    return prev + cur.quantity;
                  }, 0) + '',
                installerIds: selectedInstallerIds || '',
                itemId: cartSummary?.siteProducts[0].productId.toString(),
                limit: '4',
                mobileInstall: 'true',
                source: SiteInstallerSourceEnum.CHECKOUT,
                userZip: cartSummary?.zip ? cartSummary?.zip : '',
              },
            });

            if (response.isSuccess) {
              if (response.data.verifiedCount) {
                if (isComingFromWidget) {
                  if (
                    response.data.defaultSelectMobileInstall &&
                    hasDefaultSelectMobileInstallFromWidget
                  ) {
                    setHasDefaultSelectMobileInstall(true);
                  } else {
                    setHasDefaultSelectMobileInstall(false);
                  }
                } else {
                  if (response.data.defaultSelectMobileInstall) {
                    setHasDefaultSelectMobileInstall(true);
                  } else {
                    setHasDefaultSelectMobileInstall(false);
                  }
                }
                setHasMobileInstall(true);
              } else {
                setHasMobileInstall(false);
              }
            }
          }

          await getSiteInstallers(
            storedCartId ?? undefined,
            cartSummary?.siteProducts,
            isSimpleShop // if Simple shop use the value from widget app config
              ? selectedInstallerId === null
                ? isMobileInstallerFromWidget || false
                : isSelectedInstallerFromWidgetConfig
                  ? isMobileInstallerFromWidget || false
                  : false
              : zipChanged
                ? hasDefaultSelectMobileInstall ||
                  hasDefaultSelectMobileInstallFromWidget
                  ? true
                  : false
                : preSelectedInstallerTypeIsMobile === 'true'
                  ? true
                  : false,
          );
        }
      } finally {
        if ((zipChanged && cartProductsLength) || storedCartId) {
          setCartSummaryApiLoading(false);
        }
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [zipChanged, cartProductsLength, getSiteInstallers, storedCartId]);

  useEffect(() => {
    if (!storedCartId) {
      return;
    }

    if (!storedZipCode && !newUserLocation) {
      return;
    }

    getPickupLocations();
  }, [
    getPickupLocations,
    newUserLocation,
    storedCartId,
    storedZipCode,
    zipChanged,
  ]);

  return null;
}

const CartShippingContextEffect = memo(() => {
  const isDealerTire = useUserPersonalizationContextSelector(
    (v) => v.isDealerTire,
  );

  return (
    <>
      {isDealerTire ? (
        <CartShippingContextWarehousePickupEffect />
      ) : (
        <>
          <CartShippingContextGetInstallerScheduleEffect />
          <CartShippingContextInstallersEffect />
        </>
      )}
    </>
  );
});

export function CartShippingContextProvider({
  children,
  value: cartShippingSSR,
}: ContextProps) {
  const value = useContextSetup(cartShippingSSR);

  return (
    <CartShippingContext.Provider value={value}>
      <CartShippingContextEffect />
      {children}
    </CartShippingContext.Provider>
  );
}

export const useCartShippingContextSelector = <SelectedValue,>(
  selector: Selector<CartShippingContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<CartShippingContextProps, SelectedValue>(
    CartShippingContext,
    selector,
    equalCompareFn,
  );
