/**
 * @NOTE: this context is for handling the concerns of user actions in checkout or cart summary
 */
import { usePathname } from 'next/navigation';
import { parseCookies, setCookie } from 'nookies';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { SelectedDayAndTime } from '~/components/global/InstallationAppointmentSelectionModal/AppointmentDateTimePicker.types';
import {
  initialState,
  ShippingConfirmFormValue,
} from '~/components/global/InstallationAppointmentSelectionModal/ShippingConfirmForm/ShippingConfirmForm.types';
import { Shop } from '~/components/global/InstallationShopCard/InstallationShopCard.types';
import { useCartSummaryContextSelector } from '~/components/modules/Cart/CartSummary.context';
import {
  PANEL,
  SHIPPING_OPTIONS,
  SHIPPING_OPTIONS_LABELS,
} from '~/components/pages/CheckoutPage/checkout.constants';
import { mapToInstallerCartShippingRequest } from '~/components/pages/CheckoutPage/checkout.util';
import { useGlobalsContextSelector } from '~/context/Globals.context';
import { useUserPersonalizationContextSelector } from '~/context/UserPersonalization.context';
import { SiteCartShippingRequest } from '~/data/models/SiteCartShippingRequest';
import {
  SHIPPINGSERIVCES,
  ShippingType,
} from '~/data/models/SiteCartShippingResponse';
import {
  PRE_SHIPPING_SELECTION,
  SiteCartSummary,
} from '~/data/models/SiteCartSummary';
import {
  IDMEVerifiedStatus,
  SiteCartSummaryRequest,
} from '~/data/models/SiteCartSummaryRequest';
import { SiteInstallerItem } from '~/data/models/SiteInstallerItem';
import { SiteInstallerScheduleTimeSlotEnum } from '~/data/models/SiteInstallerScheduleTime';
import { useHasChanged } from '~/hooks/useHasChanged';
import { usePreviousState } from '~/hooks/usePreviousState';
import useRouter from '~/hooks/useRouter';
import useWidgetSource from '~/hooks/useWigetSource';
import { apiCreateSiteCartAppointment } from '~/lib/api/checkout/cart-appointment';
import { apiCreateCartServices } from '~/lib/api/checkout/cart-services';
import {
  apiCreateCartShipping,
  apiGetCartShipping,
} from '~/lib/api/checkout/cart-shipping';
import { apiDeleteCartSummary } from '~/lib/api/checkout/cart-summary';
import { apiGetSiteInstallers } from '~/lib/api/installers';
import { apiGetSiteInstallerSchedule } from '~/lib/api/installers/schedule';
import { fetchServiceNames } from '~/lib/api/serviceNames';
import { COOKIES } from '~/lib/constants/cookies';
import { ROUTE_MAP, ROUTES } from '~/lib/constants/routes';
import { PROPERTIES, SESSION_STORAGE } from '~/lib/constants/sessionStorage';
import { USER_TYPE } from '~/lib/constants/sso';
import { eventEmitters } from '~/lib/events/emitters';
import GA from '~/lib/helpers/analytics';
import { getSearchParams } from '~/lib/helpers/app-routes/search-params';
import logger from '~/lib/helpers/logger';
import { rudderstackSendTrackEvent } from '~/lib/helpers/rudderstack';
import { RudderstackTrackEventName } from '~/lib/helpers/rudderstack/events';
import { mapToProductData } from '~/lib/helpers/rudderstack/transformer';
import { ExtendedEventProperties } from '~/lib/helpers/rudderstack/types';
import { seStorage } from '~/lib/utils/browser-storage';
import {
  createContext,
  EqualCompareFn,
  Selector,
  useContextSelector,
} from '~/lib/utils/context-selector';
import { isSimpleShopDeployment } from '~/lib/utils/deploy';
import { isCheckoutPagePath } from '~/lib/utils/routes';

import { SiteInstallerSourceEnum } from '../PDP/Installers/Installers.types';
import { useCartShippingContextSelector } from './CartShipping.context';
import { cookieConfig } from './CartSummary.constants';
import {
  clearCartCookies,
  determineCurrentTab,
  mapSlotTimeToAmPm,
} from './CartSummary.utils';
import { isValidCartShipping } from './CartSummaryModal/ShippingService/shippingService.utils';
import {
  AddToCartParams,
  CartUserActionContextProps,
  ContextProps,
} from './CartUserAction.types';

const CartUserActionContext = createContext<CartUserActionContextProps>();

export function getShippingOptionByPanelIndex(
  panel: PANEL,
  isCartInstallable: boolean,
  hasMobileInstall: boolean,
  hasUninstallable: boolean,
  isCartUnInstallable: boolean,
  isDealerTire?: boolean,
  deliveryMethods?: Array<string>,
) {
  let options: Array<SHIPPING_OPTIONS> = [];

  if (isDealerTire) {
    options = [
      SHIPPING_OPTIONS.WAREHOUSE,
      SHIPPING_OPTIONS.INSTALLER,
      SHIPPING_OPTIONS.MOBILE,
      SHIPPING_OPTIONS.HOME,
      SHIPPING_OPTIONS.FEDEX,
    ];
    options = options.filter((option) =>
      deliveryMethods?.includes(SHIPPING_OPTIONS_LABELS[option]),
    );
    return options[panel];
  } else {
    if (isCartUnInstallable) {
      options = [SHIPPING_OPTIONS.HOME, SHIPPING_OPTIONS.FEDEX];
    } else if (isCartInstallable) {
      if (hasMobileInstall) {
        options = [
          SHIPPING_OPTIONS.INSTALLER,
          SHIPPING_OPTIONS.MOBILE,
          SHIPPING_OPTIONS.HOME,
          SHIPPING_OPTIONS.FEDEX,
        ];
      } else {
        options = [
          SHIPPING_OPTIONS.INSTALLER,
          SHIPPING_OPTIONS.HOME,
          SHIPPING_OPTIONS.FEDEX,
        ];
      }
    } else if (hasUninstallable) {
      options = [
        SHIPPING_OPTIONS.INSTALLER,
        SHIPPING_OPTIONS.HOME,
        SHIPPING_OPTIONS.FEDEX,
      ];
    } else {
      options = [SHIPPING_OPTIONS.INSTALLER];
    }
    return options[panel];
  }
}

function useContextSetup(): CartUserActionContextProps {
  const {
    cartSummary,
    updateCartSummary,
    cartId,
    setCartSummary,
    setCartId,
    setIsCartSummaryModalOpen,
    createCartSummary,
    setHasDefaultSelectMobileInstall,
    setHasMobileInstall,
    setShopCardValues,
    hasMobileInstall,
  } = useCartSummaryContextSelector((v) => ({
    cartId: v.cartId,
    cartSummary: v.cartSummary,
    createCartSummary: v.createCartSummary,
    hasMobileInstall: v.hasMobileInstall,
    setCartId: v.setCartId,
    setCartSummary: v.setCartSummary,
    setHasDefaultSelectMobileInstall: v.setHasDefaultSelectMobileInstall,
    setHasMobileInstall: v.setHasMobileInstall,
    setIsCartSummaryModalOpen: v.setIsCartSummaryModalOpen,
    setShopCardValues: v.setShopCardValues,
    updateAndFetchCartSummary: v.updateAndFetchCartSummary,
    updateCartSummary: v.updateCartSummary,
  }));

  const {
    cartShipping,
    cartAppointment,
    setCartAppointment,
    setCartShipping,
    updateCartShipping,
    createCartShipping,
    getSiteInstallers,
    selectedLocation,
    setSelectedLocation,
    createInstallLocation,
    warehouseSelectedLocation,
    setPrevCartProducts,
  } = useCartShippingContextSelector((v) => ({
    cartAppointment: v.cartAppointment,
    cartShipping: v.cartShipping,
    createCartAppointment: v.createCartAppointment,
    createCartShipping: v.createCartShipping,
    createInstallLocation: v.createInstallLocation,
    deleteCartAppointment: v.deleteCartAppointment,
    getSiteInstallers: v.getSiteInstallers,
    selectedLocation: v.selectedLocation,
    setCartAppointment: v.setCartAppointment,
    setCartShipping: v.setCartShipping,
    setPrevCartProducts: v.setPrevCartProducts,
    setSelectedLocation: v.setSelectedLocation,
    updateCartAppointment: v.updateCartAppointment,
    updateCartShipping: v.updateCartShipping,
    warehouseSelectedLocation: v.warehouseSelectedLocation,
  }));

  const { isDealerTire, deliveryMethods, isFleet, isSpecialOrder } =
    useUserPersonalizationContextSelector((v) => ({
      isDealerTire: v.isDealerTire,
      isFleet: v.isFleet,
      isSpecialOrder: v.userType === USER_TYPE.SPECIAL_ORDER,
      deliveryMethods: v.deliveryMethods,
    }));
  const [shouldShowpaypalAddress, setShouldShowpaypalAddress] = useState(false);
  const preSelectedInstallerTypeIsMobile = seStorage.getItem(
    SESSION_STORAGE[PROPERTIES.PRE_SELECTED_INSTALLER_TYPE],
  );
  const isMobileInstallFromWidgetConfig = seStorage.getItem(
    SESSION_STORAGE[PROPERTIES.IS_MOBILE_INSTALL],
  );

  const isSimpleShop = useGlobalsContextSelector(
    (v) => Number(v.isSimpleShop) === 1,
  );

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = getSearchParams();
  const { isComingFromWidget } = useWidgetSource();
  const isCheckoutPage = isCheckoutPagePath(pathname);
  const isOrderConfirmPage = pathname?.includes('order-confirmation');
  const hasMobileInstallInSearchParams = searchParams?.get('hasMobileInstall');
  const activeShippingTypeInSearchParams =
    searchParams?.get('activeShippingType');
  const activeShippingTypeIsMobileInstall =
    activeShippingTypeInSearchParams === SHIPPINGSERIVCES.MOBILEINSTALL;

  const initialActiveOption =
    cartShipping &&
    cartShipping?.shippingOption !== ShippingType.INSTALLER &&
    isValidCartShipping(cartShipping.cartShipping)
      ? cartShipping?.shippingOption
      : undefined;

  const [productsinCartQuantity, setProductsinCartQuantity] = useState<number>(
    cartSummary?.siteProducts.length || 0,
  );
  const [activeOption, setActiveOption] = useState<ShippingType | undefined>(
    initialActiveOption,
  );
  const [isUserSelectedShop, setIsUserSelectedShop] = useState(false);
  const [removeItemFlag, setRemoveItemFlag] = useState(false);
  const [isShippingFormOpen, setIsShippingFormOpen] = useState<boolean>(false);
  const [isPaypalShipToHomeSelected, setPaypalShipToHomeSelected] =
    useState<boolean>(false);
  const [
    isFedexLocationSelectedFromModal,
    setIsFedexLocationSelectedFromModal,
  ] = useState<boolean>(false);
  const [
    isShippingFormOpenFromCheckoutBtn,
    setIsShippingFormOpenFromCheckoutBtn,
  ] = useState<boolean>(false);
  const [confirmedData, setConfirmedData] = useState<SiteCartSummaryRequest>();
  const [pagePathPriorToCheckout, updatePagePathPriorToCheckout] =
    useState('/');
  const [redirectToShipToMe, setRedirectToShipToMe] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState<boolean>(false);
  const [isOpenTimeChangeModalOnPdp, setIsOpenTimeChangeModalOnPdp] =
    useState<boolean>(false);
  const [selectedShopOnCatalog, setSelectedShopOnCatalog] = useState<
    Shop | undefined
  >();
  const [isTabChanging, setIsTabChanging] = useState(false);
  const [isShopCardError, setIsShopCardError] = useState(false);
  const [isFedexShopCardError, setIsFedexShopCardError] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);

  const hasUninstallable = cartSummary?.siteProducts.some(
    (product) => !product.isInstallable,
  );
  const hasFewInstallable = cartSummary?.siteProducts.some(
    (product) => !!product.isInstallable,
  );
  const isCartUnInstallable = cartSummary?.siteProducts.every(
    (product) => !product.isInstallable,
  );
  const isCartInstallable = cartSummary?.siteProducts.every(
    (product) => product.isInstallable,
  );

  const disableFirstTab = (hasUninstallable && hasFewInstallable) || false;
  const initialSelectedTab = determineCurrentTab({
    cartShipping: cartShipping || null,
    disableFirstTab,
    hasMobileInstall:
      hasMobileInstall || Boolean(hasMobileInstallInSearchParams) || false,
    hasUninstallable,
    isCartInstallable,
    isCartUnInstallable,
    queryActiveShippingType:
      (activeShippingTypeInSearchParams as SHIPPINGSERIVCES) || null,
    redirectToShipToMe,
  });

  const [tabLoading, setTabLoading] = useState<boolean>(true);
  const [currentTab, setCurrentTab] = useState<PANEL>(initialSelectedTab);

  // TODO: Can substitue tabType everywhere in checkout flow
  // instead of jumping hoops to understand PANEL logic
  const [currentTabType, setCurrentTabType] = useState<SHIPPING_OPTIONS>(
    getShippingOptionByPanelIndex(
      initialSelectedTab,
      !!isCartInstallable,
      hasMobileInstall,
      !!hasUninstallable,
      !!isCartUnInstallable,
      isDealerTire,
      deliveryMethods,
    ),
  );

  const isWarehouseTabSelected = useMemo(() => {
    return isDealerTire && currentTab === PANEL.LEFT;
  }, [isDealerTire, currentTab]);

  useEffect(() => {
    let timeout: any;
    if (currentTab === initialSelectedTab) {
      timeout = setTimeout(() => {
        setTabLoading(false);
      }, 3000);
    }

    return () => {
      clearTimeout(timeout);
    };
  }, [currentTab, initialSelectedTab]);

  useEffect(() => {
    if (currentTab === undefined) {
      return;
    }

    const newTabType = getShippingOptionByPanelIndex(
      currentTab,
      !!isCartInstallable,
      hasMobileInstall,
      !!hasUninstallable,
      !!isCartUnInstallable,
      isDealerTire,
      deliveryMethods,
    );

    if (currentTabType !== newTabType) {
      setCurrentTabType(newTabType);
    }
  }, [
    currentTab,
    isCartInstallable,
    currentTabType,
    hasMobileInstall,
    hasUninstallable,
    isCartUnInstallable,
    isDealerTire,
    deliveryMethods,
  ]);

  const cartinstallable = cartSummary?.siteProducts.every(
    (product) => product.isInstallable,
  );
  const cartHasOversized = cartSummary?.siteProducts.some(
    (product) => product.isOversizeTire,
  );

  const isShipToMeTabSelected = useMemo(() => {
    return (
      (isDealerTire && currentTab === PANEL.MID) ||
      (isCartInstallable && hasMobileInstall
        ? currentTab === PANEL.RIGHT
        : currentTab === PANEL.MID) ||
      (isCartUnInstallable && currentTab === PANEL.LEFT) ||
      false
    );
  }, [
    currentTab,
    hasMobileInstall,
    isCartUnInstallable,
    isCartInstallable,
    isDealerTire,
  ]);

  const isFedexTabSelected = useMemo(() => {
    return (
      (isCartInstallable &&
        (hasMobileInstall
          ? currentTab === PANEL.END
          : currentTab === PANEL.RIGHT)) ||
      (isCartUnInstallable && !hasFewInstallable && currentTab === PANEL.MID) ||
      (hasFewInstallable &&
        !isCartInstallable &&
        !isCartUnInstallable &&
        currentTab === PANEL.RIGHT) ||
      false
    );
  }, [
    currentTab,
    hasFewInstallable,
    hasMobileInstall,
    isCartInstallable,
    isCartUnInstallable,
  ]);

  const [formValues, setFormValues] =
    useState<ShippingConfirmFormValue>(initialState);

  const cookies = parseCookies();
  const cartIdFromWidget = searchParams?.get('cartId');

  const storedCartId = cookies[COOKIES.CART_ID] || cartIdFromWidget || null;
  const { isSourcePirelliWidget } = useWidgetSource();

  const [isApiLoading, setIsApiLoading] = useState(false);
  const [isDisplayErrorNotification, setIsDisplayErrorNotification] =
    useState(false);

  useEffect(() => {
    (async () => {
      if (
        cartShipping !== undefined ||
        (cartSummary && !cartSummary?.cartShippingAddressPresent) ||
        !cartId
      ) {
        return;
      }
      const cartShippingRes = await apiGetCartShipping({
        query: { cartId },
      });
      if (cartShippingRes && cartShippingRes.isSuccess) {
        setCartShipping(cartShippingRes.data.siteCartShippingResponse);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cartSummary?.cartShippingAddressPresent, cartShipping]);

  useEffect(() => {
    const tab = determineCurrentTab({
      cartShipping: cartShipping || null,
      disableFirstTab,
      hasMobileInstall:
        hasMobileInstall || Boolean(hasMobileInstallInSearchParams) || false,
      hasUninstallable,
      isCartInstallable,
      isCartUnInstallable,
      queryActiveShippingType:
        (activeShippingTypeInSearchParams as SHIPPINGSERIVCES) || null,
      redirectToShipToMe,
    });
    setCurrentTab(() => tab);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const cleanupCart = useCallback(
    (removeShippingOnly = false) => {
      if (removeShippingOnly) {
        setCartShipping(undefined);
        setCookie(null, COOKIES.CART_SHIPPING, '', cookieConfig);
        return;
      }

      setCartSummary(null);
      setCartAppointment(undefined);
      setCartShipping(undefined);
      setCartId('');
      setCartShipping(undefined);
      clearCartCookies();
      setIsCartSummaryModalOpen(false);
      eventEmitters.updateNavCartQuantity.emit(null);
    },
    [
      setCartAppointment,
      setCartId,
      setCartShipping,
      setCartSummary,
      setIsCartSummaryModalOpen,
    ],
  );

  const clearCartOnDeleteLastItem = useCallback(async () => {
    const response = await apiDeleteCartSummary({
      query: {
        id: cartId,
      },
      includeUserRegion: true,
      includeUserZip: true,
    });

    if (response.isSuccess) {
      cleanupCart();
      if (isCheckoutPage) {
        if (!isSourcePirelliWidget) {
          await router.replace(pagePathPriorToCheckout);
        }

        eventEmitters.setEmptyCartModalVisibility.emit(true);
      }
    }
  }, [
    cartId,
    cleanupCart,
    isCheckoutPage,
    isSourcePirelliWidget,
    pagePathPriorToCheckout,
    router,
  ]);

  const checkManuallyConfirmedAppointment = (installerId: string): boolean => {
    try {
      const confirmedAppointments = sessionStorage.getItem(
        'manuallyConfirmedAppointments',
      );
      if (confirmedAppointments) {
        const appointments = JSON.parse(confirmedAppointments);
        return appointments.includes(installerId);
      }
      return false;
    } catch {
      return false;
    }
  };

  const createAppointment = useCallback(
    async (
      appointmentRequestBody: any,
      installerId: string,
      newCartId: string,
      isSimpleShop: boolean,
      isMobileInstall: boolean,
    ) => {
      if (!appointmentRequestBody || isMobileInstall) {
        return null;
      }

      const shouldCreateAppointment = isSimpleShop
        ? checkManuallyConfirmedAppointment(installerId)
        : true;

      if (shouldCreateAppointment) {
        return await apiCreateSiteCartAppointment({
          body: appointmentRequestBody,
          query: {
            id: newCartId,
          },
        });
      }

      return null;
    },
    [],
  );

  const handleAppointmentResult = useCallback(
    (appointment: any) => {
      if (appointment && appointment.isSuccess) {
        if (appointment.data === 'null') {
          setCartAppointment(null);
          return;
        }

        setCookie(null, COOKIES.CART_APPOINTMENT, '1', cookieConfig);
        setCartAppointment(appointment.data);
        if (appointment.data === null) {
          setIsOpenTimeChangeModalOnPdp(true);
        }
      }
    },
    [setCartAppointment, setIsOpenTimeChangeModalOnPdp],
  );

  const addToCart = useCallback(
    async ({
      productId,
      email,
      quantity,
      installerId,
      shouldAddCoverage,
      appointmentTime,
      vehicleMake,
      vehicleModel,
      vehicleTrim,
      vehicleYear,
    }: AddToCartParams) => {
      if (!quantity.front && !quantity.rear) {
        return;
      }
      try {
        setPrevCartProducts(cartSummary?.siteProducts ?? []);
        setIsAddingToCart(true);
        const isSimpleShop = isSimpleShopDeployment();
        let appointmentRequestBody;
        if (installerId) {
          const responseApp = await apiGetSiteInstallerSchedule({
            includeUserTime: true,
            installerId,
            query: {
              source: SiteInstallerSourceEnum.PDP,
              cartId: '',
              itemId: productId,
            },
          });
          if (responseApp.isSuccess) {
            const { date, day } =
              responseApp.data.siteInstallerSchedule.scheduleDays[0];
            const defaultScheduleData: SelectedDayAndTime = {
              date,
              day,
              isDropOff: true,
              isSlotAvailable: true,
              slotTime: SiteInstallerScheduleTimeSlotEnum.Morning,
              startTime: '',
            };

            if (appointmentTime || defaultScheduleData) {
              const { date, startTime, isDropOff, slotTime, amOrPm } =
                appointmentTime ? appointmentTime : defaultScheduleData;
              appointmentRequestBody = {
                amOrPm: mapSlotTimeToAmPm(slotTime, isDropOff, amOrPm),
                date,
                dropOffWindow: isDropOff ? slotTime : '',
                isDropOff,
                startTime,
              };
            }
          }
        }
        const handleAddToCartEvent = async (
          cartId: string,
          cartSummary: SiteCartSummary,
        ) => {
          const { mobileInstallerList, installerList } =
            await getSiteInstallersForMobileOptions(cartSummary, cartId);
          cartSummary.siteProducts.forEach((product) => {
            if (
              product.productId.toString() === productId.toString() &&
              product?.price
            ) {
              let eventData: ExtendedEventProperties = mapToProductData(
                product,
                cartId,
                quantity.front || quantity.rear || 0,
              );
              eventData = {
                ...eventData,
                installerList,
                mobileInstallerList,
                zip: cartSummary?.zip ?? '',
              };
              rudderstackSendTrackEvent(
                RudderstackTrackEventName.ADD_TO_CART,
                eventData,
              );
            }
          });
        };
        if (storedCartId) {
          setCartId(storedCartId);
          const isNewItem = !cartSummary?.siteProducts.find(
            (product) => product.productId === Number(productId),
          );
          const hasOversized = cartSummary?.siteProducts.some(
            (product) => product.oversized,
          );
          const hasUninstallable = cartSummary?.siteProducts.some(
            (product) => !product.isInstallable,
          );
          await updateCartSummary({
            excludeShipping: hasUninstallable && hasOversized ? true : false, // do not show shippingPrice on add.
            installerId: installerId || null,
            isGroupRoadHazard:
              isSpecialOrder || isFleet || isSimpleShop
                ? false
                : shouldAddCoverage,
            itemId: productId,
            itemQuantity: quantity.front || quantity.rear || 0,
            ...(isNewItem ? { isNewItem } : {}),
            vehicleMake,
            vehicleModel,
            vehicleTrim,
            vehicleYear,
          });
          if (cartSummary?.siteProducts) {
            handleAddToCartEvent(storedCartId, cartSummary);
          }
          await getSiteInstallers(
            storedCartId + '' || undefined,
            cartSummary?.siteProducts,
            currentTab === PANEL.MID ? true : false,
          );
          if (cartSummary && cartSummary.installerDetails) {
            const cartShippingRequest: SiteCartShippingRequest =
              mapToInstallerCartShippingRequest(cartSummary.installerDetails, {
                email: email || '',
              });
            const isShippingDataSet = cookies[COOKIES.CART_SHIPPING] || null;
            if (!isShippingDataSet) {
              const response = await apiCreateCartShipping({
                input: cartShippingRequest,
                query: { cartId: storedCartId },
                includeUserRegion: true,
                includeUserZip: true,
              });

              setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);
              if (response.isSuccess) {
                setCartShipping(response.data.siteCartShippingResponse);
              }
            } else {
              const response = await updateCartShipping(cartShippingRequest);

              if (response && response.isSuccess) {
                await updateCartSummary({
                  email,
                  excludeShipping: false,
                  vehicleMake,
                  vehicleModel,
                  vehicleTrim,
                  vehicleYear,
                });

                setCartShipping(response.data.siteCartShippingResponse);
              }
            }
          }

          if (appointmentRequestBody) {
            rudderstackSendTrackEvent(
              RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
              {
                checkout_id: storedCartId ?? '',
                step: 'ChooseShipping',
                shipping_method: 'ShipInstaller',
                shipToRefId: installerId || '',
              },
            );
          }

          if (installerId) {
            await updateServiceNamesOnCartOpen();

            const storedServices = seStorage.getItem(
              SESSION_STORAGE.SELECTED_SERVICES,
            );

            if (storedServices) {
              try {
                const services = JSON.parse(storedServices);
                await apiCreateCartServices(storedCartId, {
                  installerId: Number(installerId),
                  services,
                });
              } catch (error) {
                logger.error('Error parsing stored services:', error);
              }
            }
          }
        } else {
          const response = await createCartSummary({
            excludeShipping: false, // do not show shippingPrice on add.
            idMeVerifiedStatus: IDMEVerifiedStatus.NULL,
            installerId: installerId || null,
            isNewItem: true,
            isRoadHazard:
              isFleet || isSpecialOrder || isSimpleShop ? false : true, // always true when the first tire is added to the cart
            itemId: productId,
            itemQuantity: quantity.front || quantity.rear || 0,
            promoCode: null,
            vehicleMake,
            vehicleModel,
            vehicleTrim,
            vehicleYear,
            ...confirmedData,
            preShippingSelection: isShipToMeTabSelected
              ? PRE_SHIPPING_SELECTION.SHIP_TO_ME
              : isFedexTabSelected
                ? PRE_SHIPPING_SELECTION.SHIP_TO_FEDEX
                : isWarehouseTabSelected
                  ? PRE_SHIPPING_SELECTION.WAREHOUSE_PICKUP
                  : PRE_SHIPPING_SELECTION.SHIP_TO_SHOP,
          });

          if (response.isSuccess) {
            const newCartId = response.data.siteCart.id.toString();
            setCartId(newCartId);
            setCookie(
              null,
              COOKIES.CART_ID,
              response.data.siteCart.id.toString(),
              cookieConfig,
            );
            const { mobileInstallerList, installerList } =
              await getSiteInstallersForMobileOptions(
                response.data.siteCart,
                newCartId,
              );

            response.data.siteCart.siteProducts.map((product) => {
              if (product.productId.toString() == productId && product?.price) {
                let products: ExtendedEventProperties = mapToProductData(
                  product,
                  newCartId,
                  quantity.front || quantity.rear || 0,
                );
                products = {
                  ...products,
                  installerList,
                  mobileInstallerList,
                  zip: response.data.siteCart.zip ?? '',
                };

                rudderstackSendTrackEvent(
                  RudderstackTrackEventName.ADD_TO_CART,
                  products,
                );
                // Do not change anything in the following two lines
                window.VWO = window.VWO || [];
                window.VWO.event =
                  window.VWO.event ||
                  function () {
                    // eslint-disable-next-line prefer-rest-params
                    window.VWO.push(['event'].concat([].slice.call(arguments)));
                  };

                // Replace the property values with your actual values
                window.VWO.event('rudder.ADD_TO_CART', {
                  brand: product.brand.label,
                  category: product.productSubType,
                  price: product.price.salePriceInCents,
                  product_id: product.productId,
                  quantity: product.quantity,
                  variant: product.size,
                });
              }
            });

            GA.addToDataLayer({
              cartAddInstallerId: installerId || undefined,
              cartAddIsRoadHazard: shouldAddCoverage.toString(),
              cartAddItemId: productId,
              cartAddItemQuantity: quantity.front || quantity.rear || 0,
              event: 'isCartAdd',
            });

            if (installerId && response.data.siteCart.installerDetails) {
              const cartShippingRequest: SiteCartShippingRequest =
                mapToInstallerCartShippingRequest(
                  response.data.siteCart.installerDetails,
                  { email: email || '' },
                );
              GA.addToDataLayer({
                cartAddInstallerId: installerId || undefined,
                cartAddIsRoadHazard: shouldAddCoverage.toString(),
                cartAddItemId: productId,
                cartAddItemQuantity: quantity.front || quantity.rear || 0,
                event: 'isCartAdd',
              });
              rudderstackSendTrackEvent(
                RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
                {
                  checkout_id: storedCartId ?? '',
                  step: 'ChooseShipping',
                  shipping_method: 'ShipInstaller',
                  shipToRefId: installerId || '',
                },
              );
              if (appointmentRequestBody) {
                GA.addToDataLayer({
                  cartSummary,
                  event: 'isCheckoutStep',
                  shipToOption: 'ShipInstaller',
                  shipToRefId: installerId,
                  stepName: 'ScheduleInstallation',
                });
                rudderstackSendTrackEvent(
                  RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
                  {
                    checkout_id: storedCartId ?? '',
                    step: 'ScheduleInstallation',
                    shipping_method: 'ShipInstaller',
                    shipToRefId: installerId,
                  },
                );
              }

              const res = await apiCreateCartShipping({
                input: cartShippingRequest,
                query: { cartId: newCartId },
                includeUserRegion: true,
                includeUserZip: true,
              });

              if (res.isSuccess) {
                setCookie(null, COOKIES.CART_SHIPPING, '1', cookieConfig);
                setCartShipping(res.data.siteCartShippingResponse);
                // create appointment on add to cart only for local installers. For mobile just select the shop and dont confirm the appointment.
                const isMobileInstall =
                  res.data?.siteCartShippingResponse &&
                  res.data?.siteCartShippingResponse.cartShipping.installer
                    ?.isMobileInstall;
                const appointment = await createAppointment(
                  appointmentRequestBody,
                  installerId,
                  newCartId,
                  Boolean(isSimpleShop),
                  Boolean(isMobileInstall),
                );

                handleAppointmentResult(appointment);
              }
            }

            if (installerId) {
              await updateServiceNamesOnCartOpen();

              const storedServices = seStorage.getItem(
                SESSION_STORAGE.SELECTED_SERVICES,
              );

              if (storedServices) {
                try {
                  const services = JSON.parse(storedServices);
                  await apiCreateCartServices(newCartId, {
                    installerId: Number(installerId),
                    services,
                  });
                } catch (error) {
                  logger.error('Error parsing stored services:', error);
                }
              }
            }
          }
        }
      } catch (error) {
        logger.error('Error when adding to cart', error);
      } finally {
        setIsAddingToCart(false);
      }
    },
    [
      storedCartId,
      setCartId,
      cartSummary,
      updateCartSummary,
      getSiteInstallers,
      isFleet,
      isSpecialOrder,
      currentTab,
      cookies,
      setCartShipping,
      updateCartShipping,
      createCartSummary,
      confirmedData,
      isShipToMeTabSelected,
      isFedexTabSelected,
      isWarehouseTabSelected,
      setPrevCartProducts,
      createAppointment,
      handleAppointmentResult,
    ],
  );

  async function getSiteInstallersForMobileOptions(
    cartSummary: SiteCartSummary | null,
    cartId: string,
  ): Promise<{
    installerList: SiteInstallerItem[];
    mobileInstallerList: SiteInstallerItem[];
  }> {
    const frontQuantity =
      cartSummary?.siteProducts.reduce((prev, cur) => {
        return prev + cur.quantity;
      }, 0) + '';

    const itemId = cartSummary?.siteProducts[0].productId.toString() || '';
    const userZip = cartSummary?.zip ? cartSummary?.zip : '';

    const [mobileInstallers, installers] = await Promise.all([
      apiGetSiteInstallers({
        query: {
          cartId,
          frontQuantity,
          itemId,
          mobileInstall: 'true',
          source: SiteInstallerSourceEnum.CHECKOUT,
          userZip,
        },
      }),
      apiGetSiteInstallers({
        query: {
          cartId,
          frontQuantity,
          itemId,
          mobileInstall: 'false',
          source: SiteInstallerSourceEnum.CHECKOUT,
          userZip,
        },
      }),
    ]);

    const mobileInstallerList = mobileInstallers.isSuccess
      ? mobileInstallers.data.siteInstallers.siteInstallerList
      : [];
    const installerList = installers.isSuccess
      ? installers.data.siteInstallers.siteInstallerList
      : [];

    return { mobileInstallerList, installerList };
  }

  const removeCartProduct = useCallback(
    async (productId: number) => {
      const hasOtherProducts = (cartSummary?.siteProducts || []).some(
        (siteProduct) => siteProduct.productId !== productId,
      );
      cartSummary?.siteProducts.map((product) => {
        if (product.productId == productId && product?.price) {
          const addToCartRudderstack = mapToProductData(product, cartId, 0);
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.REMOVE_PRODUCT_FROM_CART,
            addToCartRudderstack,
          );
        }
      });
      if (hasOtherProducts) {
        setRemoveItemFlag(true);
        await updateCartSummary(
          {
            itemId: productId.toString(),
            itemQuantity: 0,
          },
          undefined,
          true,
        );
      } else {
        setShopCardValues(undefined);
        setCurrentTab(PANEL.LEFT);
        await clearCartOnDeleteLastItem();
      }
    },
    [
      cartSummary?.siteProducts,
      cartId,
      updateCartSummary,
      setShopCardValues,
      clearCartOnDeleteLastItem,
    ],
  );

  const handleCheckout = useCallback(async () => {
    if (
      !cartSummary ||
      !cartSummary.siteProducts ||
      !cartSummary.siteProducts.length ||
      !cartShipping
    ) {
      logger.error(
        'Cart summary or cart shipping data is not set for payment.',
      );
      return;
    }

    setIsApiLoading(true);
    try {
      const { installerDetails, email } = cartSummary;
      updatePagePathPriorToCheckout(window.location.href);

      if (
        !cartHasOversized &&
        cartinstallable &&
        (currentTab === PANEL.LEFT ||
          (hasMobileInstall && currentTab === PANEL.MID))
      ) {
        // this is for installable
        if (
          installerDetails &&
          cartShipping.shippingOption !== ShippingType.INSTALLER
        ) {
          const cartShippingRequest = mapToInstallerCartShippingRequest(
            installerDetails,
            { email },
          );

          const isShippingDataSet = cookies[COOKIES.CART_SHIPPING] || null;
          if (isShippingDataSet) {
            await updateCartShipping(cartShippingRequest);
          } else {
            await createCartShipping(cartShippingRequest);
          }

          if (installerDetails.website === 'fedex.com') {
            GA.addToDataLayer({
              cartSummary,
              event: 'isCheckoutStep',
              shipToOption: 'ShipPickup',
              shipToRefId: cartShipping?.cartShipping.id,
              stepName: 'ChooseShipping',
            });
            rudderstackSendTrackEvent(
              RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
              {
                checkout_id: storedCartId ?? '',
                step: 'ChooseShipping',
                shipping_method: 'ShipPickup',
                shipToRefId: cartShipping?.cartShipping.id,
              },
            );
          } else {
            GA.addToDataLayer({
              cartSummary,
              event: 'isCheckoutStep',
              shipToOption: 'ShipInstaller',
              shipToRefId: installerDetails.installerId,
              stepName: 'ChooseShipping',
            });
            rudderstackSendTrackEvent(
              RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
              {
                checkout_id: storedCartId ?? '',
                step: 'ChooseShipping',
                shipping_method: 'ShipInstaller',
                shipToRefId: installerDetails.installerId,
              },
            );
          }
        }

        if (cartAppointment) {
          GA.addToDataLayer({
            cartSummary,
            event: 'isCheckoutStep',
            shipToOption: 'ShipInstaller',
            shipToRefId: cartShipping.cartShipping.installer?.installerId,
            stepName: 'ChooseShipping',
          });
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
            {
              checkout_id: storedCartId ?? '',
              step: 'ChooseShipping',
              shipping_method: 'ShipInstaller',
              shipToRefId:
                cartShipping.cartShipping.installer?.installerId || '',
            },
          );
        }
      } else {
        // this is for Ship-to-me and has two options home or fedex
        if (activeOption === ShippingType.HOME) {
          if (
            !cartShipping ||
            cartShipping.shippingOption !== ShippingType.HOME ||
            !isValidCartShipping(cartShipping.cartShipping)
          ) {
            setIsShippingFormOpenFromCheckoutBtn(true);
            setIsCartSummaryModalOpen(true);
            setIsShippingFormOpen(true);
            setIsApiLoading(false);
            return;
          }
        }

        if (activeOption === ShippingType.FEDEX) {
          GA.addToDataLayer({
            cartSummary,
            event: 'isCheckoutStep',
            shipToOption: 'ShipPickup',
            shipToRefId: cartShipping?.cartShipping.id,
            stepName: 'ChooseShipping',
          });
          rudderstackSendTrackEvent(
            RudderstackTrackEventName.CHECKOUT_STEP_VIEWED,
            {
              checkout_id: storedCartId ?? '',
              shipping_method: 'ShipPickup',
              shipToRefId: cartShipping?.cartShipping.id,
              step: 'ChooseShipping',
            },
          );
        }
      }

      setIsCartSummaryModalOpen(false);
      if (!isCheckoutPage) {
        await router.push(
          `${ROUTE_MAP[ROUTES.CHECKOUT_PAYMENT]}?redirectFrom=cart_modal`,
        );
      }
      return;
    } finally {
      setIsApiLoading(false);
    }
  }, [
    cartSummary,
    cartShipping,
    router,
    cartHasOversized,
    cartinstallable,
    currentTab,
    hasMobileInstall,
    setIsCartSummaryModalOpen,
    isCheckoutPage,
    cartAppointment,
    updateCartShipping,
    createCartShipping,
    storedCartId,
    activeOption,
    cookies,
  ]);

  /**
   * when removing the installerDetails, service removes the cartShipping and cart-appointment if that exists.
   * so, steer just needs to remove appointment.
   */
  const removeInstaller = useCallback(
    async (excludeShipping?: boolean, signal?: AbortSignal) => {
      if (!cartSummary?.installerDetails?.installerId) {
        return;
      }

      const res = await updateCartSummary(
        {
          installerId: null,
          removeInstallerId: cartSummary.installerDetails.installerId,
          excludeShipping,
        },
        signal,
      );

      if (res && (res.isSuccess || res.error.statusCode === 404)) {
        setCookie(null, COOKIES.CART_SHIPPING, '', cookieConfig);
        setCartShipping(undefined);
        setCookie(null, COOKIES.CART_APPOINTMENT, '', cookieConfig);
        setCartAppointment(undefined);
      }
    },
    [
      cartSummary?.installerDetails?.installerId,
      updateCartSummary,
      setCartShipping,
      setCartAppointment,
    ],
  );

  const addInstaller = useCallback(
    async (installerId: string) => {
      if (!cartId) {
        return;
      }
      await updateCartSummary({
        installerId,
      });
    },
    [cartId, updateCartSummary],
  );

  const hasValidShop = useMemo(() => {
    return (
      [PANEL.LEFT, PANEL.MID].includes(currentTab) &&
      cartShipping?.shippingOption === ShippingType.INSTALLER &&
      !!cartShipping?.cartShipping.addressLine1
    );
  }, [
    cartShipping?.cartShipping.addressLine1,
    currentTab,
    cartShipping?.shippingOption,
  ]);

  const hasValidShippingInfo = useMemo(() => {
    return (
      (activeOption === ShippingType.WAREHOUSE &&
        !!warehouseSelectedLocation) ||
      ((isCartUnInstallable ||
        (isCartUnInstallable && currentTab === PANEL.MID) ||
        (isCartInstallable && hasMobileInstall && currentTab === PANEL.END) ||
        (isCartInstallable && currentTab === PANEL.RIGHT) ||
        ((hasFewInstallable || !hasMobileInstall) &&
          currentTab === PANEL.MID) ||
        (!isCartInstallable &&
          !isCartUnInstallable &&
          !hasMobileInstall &&
          currentTab === PANEL.RIGHT)) &&
        ((activeOption === ShippingType.HOME &&
          cartShipping?.shippingOption === ShippingType.HOME &&
          isValidCartShipping(cartShipping?.cartShipping)) ||
          (activeOption === ShippingType.FEDEX && !!selectedLocation)))
    );
  }, [
    isCartUnInstallable,
    currentTab,
    isCartInstallable,
    hasMobileInstall,
    hasFewInstallable,
    activeOption,
    cartShipping?.shippingOption,
    cartShipping?.cartShipping,
    selectedLocation,
    warehouseSelectedLocation,
  ]);

  const hasNoAppointment = useMemo(() => {
    return isDealerTire
      ? false
      : cartAppointment === undefined
        ? cartShipping?.shippingOption === undefined ||
          (isCartInstallable && PANEL.LEFT === currentTab) ||
          (isCartInstallable && hasMobileInstall && PANEL.MID === currentTab)
        : (PANEL.LEFT === currentTab &&
            cartAppointment?.installer?.isMobileInstall) ||
          (PANEL.MID === currentTab &&
            !cartAppointment?.installer?.isMobileInstall);
  }, [
    cartAppointment,
    cartShipping?.shippingOption,
    currentTab,
    hasMobileInstall,
    isCartInstallable,
    isDealerTire,
  ]);

  const checkMobileInstall = useCallback(async () => {
    setIsApiLoading(true);
    const hasDefaultSelectMobileInstallFromWidget = isComingFromWidget
      ? hasMobileInstallInSearchParams && activeShippingTypeIsMobileInstall
      : activeShippingTypeIsMobileInstall;

    const response = await apiGetSiteInstallers({
      query: {
        cartId,
        frontQuantity:
          cartSummary?.siteProducts.reduce((prev, cur) => {
            return prev + cur.quantity;
          }, 0) + '',
        itemId: cartSummary?.siteProducts[0].productId.toString() || '',
        mobileInstall: 'true',
        source: SiteInstallerSourceEnum.CHECKOUT,
        userZip: cartSummary?.zip ? cartSummary?.zip : '',
      },
    });
    if (response.isSuccess) {
      if (
        isComingFromWidget &&
        hasDefaultSelectMobileInstallFromWidget &&
        response.data.defaultSelectMobileInstall
      ) {
        setHasDefaultSelectMobileInstall(true);
      } else if (
        !isComingFromWidget &&
        response.data.defaultSelectMobileInstall
      ) {
        setHasDefaultSelectMobileInstall(true);
      } else {
        setHasDefaultSelectMobileInstall(false);
      }

      if (response.data.verifiedCount && response.data.verifiedCount > 0) {
        setHasMobileInstall(true);
      } else {
        setHasMobileInstall(false);
      }
    }
    setIsApiLoading(false);
  }, [
    isComingFromWidget,
    hasMobileInstallInSearchParams,
    activeShippingTypeIsMobileInstall,
    cartId,
    cartSummary?.siteProducts,
    cartSummary?.zip,
    setHasDefaultSelectMobileInstall,
    setHasMobileInstall,
  ]);

  useEffect(() => {
    if (
      formValues &&
      formValues.zip &&
      hasMobileInstall &&
      currentTab === PANEL.MID &&
      !isOrderConfirmPage
    ) {
      createInstallLocation(formValues);
    }
  }, [
    formValues,
    currentTab,
    createInstallLocation,
    hasMobileInstall,
    isOrderConfirmPage,
  ]);

  const hasValidShippingPlace = useMemo(() => {
    return (
      (!isShipToMeTabSelected &&
        !isFedexTabSelected &&
        !isWarehouseTabSelected &&
        hasValidShop &&
        !hasNoAppointment) ||
      hasValidShippingInfo
    );
  }, [
    hasValidShippingInfo,
    hasNoAppointment,
    hasValidShop,
    isFedexTabSelected,
    isShipToMeTabSelected,
    isWarehouseTabSelected,
  ]);

  //redirect to last catalog page on empty cart from multiple SKUs
  useEffect(() => {
    if (
      removeItemFlag &&
      isCheckoutPage &&
      (!cartSummary ||
        !cartSummary.siteProducts ||
        !cartSummary.siteProducts.length)
    ) {
      if (!isSourcePirelliWidget) {
        router.replace(pagePathPriorToCheckout);
      }
      eventEmitters.setEmptyCartModalVisibility.emit(true);
    }
  }, [
    isCheckoutPage,
    cartSummary,
    router,
    pagePathPriorToCheckout,
    removeItemFlag,
    isSourcePirelliWidget,
  ]);

  useEffect(() => {
    if (
      cartSummary &&
      productsinCartQuantity >= 1 &&
      productsinCartQuantity !== cartSummary?.siteProducts.length
    ) {
      setProductsinCartQuantity(cartSummary?.siteProducts.length);
    }
  }, [
    cartSummary,
    cartSummary?.siteProducts,
    productsinCartQuantity,
    setProductsinCartQuantity,
  ]);

  // when cart products are changed set current tab to ship to shop
  const productsChanged = useHasChanged(productsinCartQuantity, true);

  useEffect(() => {
    if (
      cartShipping &&
      cartShipping?.cartShipping?.shippingLocationId &&
      cartShipping?.shippingOption === ShippingType.FEDEX &&
      !selectedLocation
    ) {
      setSelectedLocation(cartShipping?.cartShipping?.shippingLocationId);
    }
    if (!productsChanged) {
      return;
    }
    if (
      disableFirstTab ||
      redirectToShipToMe ||
      preSelectedInstallerTypeIsMobile === 'true'
    ) {
      setCurrentTab(PANEL.MID);
    }
    // when products are changed from non-installable/oversize to installable/non-oversize check availability of mobile install
    if (!isSimpleShop) {
      checkMobileInstall(); // No need to check for mobile install as we get the value from widget app config
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productsChanged, setCurrentTab, disableFirstTab]);

  useEffect(() => {
    (async () => {
      if (!cartSummary?.siteProducts.length) {
        return;
      }
      if (!isSimpleShop) {
        checkMobileInstall();
      }
      if (isComingFromWidget) {
        await getSiteInstallers(
          storedCartId ?? undefined,
          cartSummary?.siteProducts,
          cartShipping?.cartShipping.installer?.isMobileInstall
            ? true
            : currentTab === PANEL.MID
              ? true
              : false,
        );
      } else {
        await getSiteInstallers(
          storedCartId ?? undefined,
          cartSummary?.siteProducts,
          isSimpleShop && isMobileInstallFromWidgetConfig
            ? true
            : cartShipping?.cartShipping.installer?.isMobileInstall
              ? true
              : currentTab === PANEL.MID
                ? true
                : false,
          isComingFromWidget,
        );
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cartSummary?.siteProducts.length]);

  // update cartsummary with preShipping selection once the tab is changed on product change
  useEffect(() => {
    (async () => {
      if (productsChanged) {
        const shippingType = getShippingOptionByPanelIndex(
          currentTab,
          !!isCartInstallable,
          hasMobileInstall,
          !!hasUninstallable,
          !!isCartUnInstallable,
        );
        let preShippingSelection;
        switch (shippingType) {
          case SHIPPING_OPTIONS.INSTALLER:
          case SHIPPING_OPTIONS.MOBILE:
            preShippingSelection = PRE_SHIPPING_SELECTION.SHIP_TO_SHOP;
            break;
          case SHIPPING_OPTIONS.HOME:
            preShippingSelection = PRE_SHIPPING_SELECTION.SHIP_TO_ME;
            break;
          case SHIPPING_OPTIONS.FEDEX:
            preShippingSelection = PRE_SHIPPING_SELECTION.SHIP_TO_FEDEX;
            break;
        }
        await updateCartSummary({ preShippingSelection });
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productsChanged]);

  useEffect(() => {
    if (isFedexShopCardError) {
      setIsFedexLocationSelectedFromModal(true);
    }
  }, [isFedexShopCardError]);

  const updateServiceNamesOnCartOpen = async () => {
    if (!isSimpleShopDeployment()) {
      return;
    }

    try {
      const storedServices = seStorage.getItem(
        SESSION_STORAGE.SELECTED_SERVICES,
      );
      if (!storedServices) {
        return;
      }

      const services = JSON.parse(storedServices);

      const hasPlaceholderNames = services.some(
        (service: any) =>
          service.serviceName && service.serviceName.startsWith('Service '),
      );

      if (!hasPlaceholderNames) {
        return;
      }

      const installerId = seStorage.getItem(SESSION_STORAGE.INSTALLER_IDS);
      if (!installerId) {
        return;
      }

      const response = await fetchServiceNames(installerId);

      if (!response || !response.isSuccess) {
        logger.error('ServiceNames - Failed to fetch service names');
      }
    } catch (error) {
      logger.error(
        'ServiceNames - Error updating service names on cart open:',
        error,
      );
    }
  };

  return {
    activeOption,
    addInstaller,
    addToCart,
    cleanupCart,
    confirmedData,
    currentTab,
    currentTabType,
    formValues,
    handleCheckout,
    hasNoAppointment,
    hasValidShippingInfo,
    hasValidShippingPlace,
    hasValidShop,
    isAddingToCart,
    isApiLoading,
    isCanceling,
    isDisplayErrorNotification,
    isFedexLocationSelectedFromModal,
    isFedexShopCardError,
    isFedexTabSelected,
    isOpenTimeChangeModalOnPdp,
    isPaypalShipToHomeSelected,
    isShippingFormOpen,
    isShippingFormOpenFromCheckoutBtn,
    isShipToMeTabSelected,
    isShopCardError,
    isTabChanging,
    isUserSelectedShop,
    isWarehouseTabSelected,
    redirectToShipToMe,
    removeCartProduct,
    removeInstaller,
    selectedShopOnCatalog,
    setActiveOption,
    setConfirmedData,
    setCurrentTab,
    setCurrentTabType,
    setFormValues,
    setIsApiLoading,
    setIsCanceling,
    setIsDisplayErrorNotification,
    setIsFedexLocationSelectedFromModal,
    setIsFedexShopCardError,
    setIsOpenTimeChangeModalOnPdp,
    setIsShippingFormOpen,
    setIsShippingFormOpenFromCheckoutBtn,
    setIsShopCardError,
    setIsTabChanging,
    setIsUserSelectedShop,
    setPaypalShipToHomeSelected,
    setRedirectToShipToMe,
    setSelectedShopOnCatalog,
    setShouldShowpaypalAddress,
    shouldShowpaypalAddress,
    tabLoading,
  };
}

function CartUserActionFlagEffect() {
  const triggerCartCleanup = useCartSummaryContextSelector(
    (v) => v.triggerCartCleanup,
  );
  const cleanupCart = useCartUserActionContextSelector((v) => v.cleanupCart);
  const prevFlag = usePreviousState(triggerCartCleanup.flag);

  useEffect(() => {
    const { flag, removeShippingOnly } = triggerCartCleanup;

    if (flag === prevFlag || flag <= -1) {
      return;
    }

    logger.info('Info! clean up cart');
    cleanupCart(removeShippingOnly);
  }, [cleanupCart, triggerCartCleanup, prevFlag]);

  return null;
}

export function CartUserActionContextProvider({ children }: ContextProps) {
  const value = useContextSetup();

  return (
    <CartUserActionContext.Provider value={value}>
      <CartUserActionFlagEffect />
      {children}
    </CartUserActionContext.Provider>
  );
}

export const useCartUserActionContextSelector = <SelectedValue,>(
  selector: Selector<CartUserActionContextProps, SelectedValue>,
  equalCompareFn?: EqualCompareFn,
) =>
  useContextSelector<CartUserActionContextProps, SelectedValue>(
    CartUserActionContext,
    selector,
    equalCompareFn,
  );
