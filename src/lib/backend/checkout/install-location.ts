import { SiteInstallLocationRequest } from '~/data/models/SiteInstallLocationRequest';
import { SiteInstallLocationApiResponse } from '~/data/models/SiteInstallLocationResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetCartInstallLocation(
  { cartId }: { cartId: string },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteInstallLocationApiResponse>({
    endpoint: '/v2/site/cart/{cartId}/install-location',
    extraQueryParams,
    includeAuthorization: true,
    includeUserSSOUid: true,
    method: 'get',
    params: {
      cartId,
    },
  });
}

export async function backendCreateCartInstallLocation(
  {
    cartId,
    input,
  }: {
    cartId: string;
    input: SiteInstallLocationRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteInstallLocationApiResponse,
    SiteInstallLocationRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/install-location',
    extraQueryParams,
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    jsonBody: input,
    method: 'post',
    params: {
      cartId,
    },
  });
}

export async function backendUpdateCartInstallLocation(
  {
    cartId,
    input,
  }: {
    cartId: string;
    input: SiteInstallLocationRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteInstallLocationApiResponse,
    SiteInstallLocationRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/install-location',
    extraQueryParams,
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    jsonBody: input,
    method: 'put',
    params: {
      cartId,
    },
  });
}

export async function backendDeleteCartInstallLocation(
  {
    cartId,
  }: {
    cartId: string;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteInstallLocationApiResponse,
    SiteInstallLocationRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/install-location',
    extraQueryParams,
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    method: 'delete',
    params: {
      cartId,
    },
  });
}
