import { SiteCartSummaryRequest } from '~/data/models/SiteCartSummaryRequest';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetCartSummary(
  {
    id,
    query,
  }: {
    id: string;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteCartSummaryResponse>({
    endpoint: '/v2/site/cart/{id}',
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
    params: {
      id,
    },
    query,
  });
}

export async function backendUpdateCartSummary(
  {
    id,
    input,
  }: {
    id: string;
    input: SiteCartSummaryRequest;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartSummaryResponse,
    SiteCartSummaryRequest
  >({
    endpoint: '/v2/site/cart/{id}',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'put',
    params: {
      id,
    },
  });
}

export async function backendCreateCartSummary(
  input: SiteCartSummaryRequest,
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartSummaryResponse,
    SiteCartSummaryRequest
  >({
    endpoint: '/v2/site/cart',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
  });
}

export async function backendDeleteCartSummary(
  {
    id,
    query,
  }: {
    id: string;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteCartSummaryResponse>({
    endpoint: '/v2/site/cart/{id}',
    extraQueryParams,
    includeAuthorization: true,
    method: 'delete',
    params: {
      id,
    },
    query,
  });
}
