import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';
import { AsyncResponse } from '~/lib/fetch/index.types';

interface BackendCreateCartServicesProps {
  id: string;
  input: {
    installerId: number;
    services: Array<{
      imagesUrl: string;
      notes: string;
      serviceId: number;
      serviceName: string;
    }>;
    sessionId: string;
  };
}

interface BackendGetCartServicesProps {
  id: string;
  input: {
    sessionId: string;
  };
}

export async function backendCreateCartServices(
  props: BackendCreateCartServicesProps,
  extraQueryParams?: Record<string, string>,
): Promise<AsyncResponse<SiteCartSummaryResponse>> {
  return await fetchWithErrorHandling<
    SiteCartSummaryResponse,
    typeof props.input
  >({
    endpoint: `/v2/site/cart/${props.id}/services`,
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: props.input,
    method: 'post',
  });
}

export async function backendGetCartServices(
  props: BackendGetCartServicesProps,
  extraQueryParams?: Record<string, string>,
): Promise<AsyncResponse<SiteCartSummaryResponse>> {
  return await fetchWithErrorHandling<SiteCartSummaryResponse>({
    endpoint: `/v2/site/cart/${props.id}/services`,
    extraQueryParams,
    includeAuthorization: true,
    method: 'get',
  });
}
