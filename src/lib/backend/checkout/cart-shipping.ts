import { SiteCartShippingRequest } from '~/data/models/SiteCartShippingRequest';
import { SiteCartShippingApiResponse } from '~/data/models/SiteCartShippingResponse';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendGetCartShipping(
  {
    cartId,
    query,
  }: {
    cartId: string;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteCartShippingApiResponse>({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    extraQueryParams,
    method: 'get',
    params: {
      cartId,
    },
    query,
  });
}

export async function backendDeleteCartShipping(
  {
    cartId,
    query,
  }: {
    cartId: string;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<SiteCartShippingApiResponse>({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    extraQueryParams,
    includeAuthorization: true,
    method: 'delete',
    params: {
      cartId,
    },
    query,
  });
}

export async function backendUpdateCartShipping(
  {
    cartId,
    query,
    input,
  }: {
    cartId: string;
    input: SiteCartShippingRequest;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartShippingApiResponse,
    SiteCartShippingRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'put',
    params: {
      cartId,
    },
    query,
  });
}

export async function backendCreateCartShipping(
  {
    cartId,
    query,
    input,
  }: {
    cartId: string;
    input: SiteCartShippingRequest;
    query?: Record<string, string>;
  },
  extraQueryParams?: Record<string, string>,
) {
  return await fetchWithErrorHandling<
    SiteCartShippingApiResponse,
    SiteCartShippingRequest
  >({
    endpoint: '/v2/site/cart/{cartId}/shipping',
    extraQueryParams,
    includeAuthorization: true,
    jsonBody: input,
    method: 'post',
    params: {
      cartId,
    },
    query,
  });
}
