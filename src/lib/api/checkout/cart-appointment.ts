import { SiteCartAppointmentRequest } from '~/data/models/SiteCartAppointmentRequest';
import { SiteCartAppointmentResponse } from '~/data/models/SiteCartAppointmentResponse';

import { fetchWithErrorHandling } from '../../fetch';

export async function apiGetSiteCartAppointment({
  query,
}: {
  query: Record<string, string>;
}) {
  return await fetchWithErrorHandling<SiteCartAppointmentResponse | 'null'>({
    endpoint: '/checkout/cart-appointment',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserTime: true,
    includeUserZip: true,
    method: 'get',
    query,
  });
}

export async function apiUpdateSiteCartAppointment({
  body,
  query,
  signal,
}: {
  body?: SiteCartAppointmentRequest;
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteCartAppointmentResponse | 'null',
    SiteCartAppointmentRequest
  >({
    endpoint: '/checkout/cart-appointment',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserTime: true,
    includeUserZip: true,
    jsonBody: body,
    method: 'put',
    query,
    signal,
  });
}

export async function apiCreateSiteCartAppointment({
  body,
  query,
  signal,
}: {
  body?: SiteCartAppointmentRequest;
  query?: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteCartAppointmentResponse | 'null',
    SiteCartAppointmentRequest
  >({
    endpoint: '/checkout/cart-appointment',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserTime: true,
    includeUserZip: true,
    jsonBody: body,
    method: 'post',
    query,
    signal,
  });
}

export async function apiDeleteSiteCartAppointment({
  signal,
  query,
}: {
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteCartAppointmentResponse | null,
    SiteCartAppointmentRequest
  >({
    endpoint: '/checkout/cart-appointment',
    includeAuthorization: true,
    includeUserRegion: true,
    includeUserTime: true,
    includeUserZip: true,
    method: 'delete',
    query,
    signal,
  });
}
