import { SiteCartSummaryRequest } from '~/data/models/SiteCartSummaryRequest';
import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';
import { fetchWithErrorHandling } from '~/lib/fetch';

export async function apiGetSiteCartSummary({
  query,
  signal,
  includeUserRegion,
  includeUserZip,
}: {
  includeUserRegion: boolean;
  includeUserZip: boolean;
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<SiteCartSummaryResponse, null>({
    endpoint: '/checkout/cart-summary',
    includeAuthorization: true,
    includeUserRegion,
    includeUserSessionId: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip,
    method: 'get',
    query,
    signal,
  });
}

export async function apiUpdateSiteCartSummary({
  input,
  query,
  signal,
  includeUserRegion,
  includeUserZip,
}: {
  includeUserRegion: boolean;
  includeUserZip: boolean;
  input?: SiteCartSummaryRequest;
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteCartSummaryResponse,
    SiteCartSummaryRequest
  >({
    endpoint: '/checkout/cart-summary',
    includeAuthorization: true,
    includeUserRegion,
    includeUserSessionId: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip,
    jsonBody: input,
    method: 'put',
    query,
    signal,
  });
}

export async function apiCreateSiteCartSummary({
  input,
  query,
  signal,
  includeUserRegion,
  includeUserZip,
}: {
  includeUserRegion: boolean;
  includeUserZip: boolean;
  input: SiteCartSummaryRequest;
  query?: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<
    SiteCartSummaryResponse,
    SiteCartSummaryRequest
  >({
    endpoint: '/checkout/cart-summary',
    includeAuthorization: true,
    includeUserRegion,
    includeUserSessionId: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip,
    jsonBody: input,
    method: 'post',
    query,
    signal,
  });
}

export async function apiDeleteCartSummary({
  query,
  signal,
  includeUserRegion,
  includeUserZip,
}: {
  includeUserRegion: boolean;
  includeUserZip: boolean;
  query: Record<string, string>;
  signal?: AbortSignal;
}) {
  return await fetchWithErrorHandling<SiteCartSummaryResponse, null>({
    endpoint: '/checkout/cart-summary',
    includeAuthorization: true,
    includeUserRegion,
    includeUserSessionId: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip,
    method: 'delete',
    query,
    signal,
  });
}
