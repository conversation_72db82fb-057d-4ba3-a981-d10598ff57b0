import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendCreateCartSummary,
  backendDeleteCartSummary,
  backendGetCartSummary,
  backendUpdateCartSummary,
} from '~/lib/backend/checkout/cart-summary';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import logger from '~/lib/helpers/logger';
import { getStringifiedParams } from '~/lib/utils/routes';

export async function GET(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id, ...rest } = getStringifiedParams(query);

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip: true,
    query,
  });

  const res = await backendGetCartSummary(
    { id, query: rest },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip: true,
    query,
  });

  const userSessionId = extraQueryParams.xUserSessionId;

  if (!userSessionId) {
    logger.error('Invalid authentication');
    return new Response(null, { status: 204 });
  }

  const body = await request.json();

  const res = await backendCreateCartSummary(
    {
      sessionId: userSessionId,
      ...body,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}

export async function PUT(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);

  const { id } = getStringifiedParams(query);

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip: true,
    query,
  });

  const userSessionId = extraQueryParams.xUserSessionId;

  if (!userSessionId) {
    logger.error('Invalid authentication');
    return new Response(null, { status: 204 });
  }

  const body = await request.json();

  const res = await backendUpdateCartSummary(
    {
      id,
      input: {
        sessionId: userSessionId,
        ...body,
      },
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}

export async function DELETE(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id, ...rest } = getStringifiedParams(query);

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserTime: true,
    includeUserVwo: true,
    includeUserZip: true,
    query,
  });

  const res = await backendDeleteCartSummary(
    { id, query: rest },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(null, { status: 204 });
  }

  return new Response(null, {
    status: res.error.statusCode,
  });
}
