import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendCreateCartShipping,
  backendDeleteCartShipping,
  backendGetCartShipping,
  backendUpdateCartShipping,
} from '~/lib/backend/checkout/cart-shipping';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import { getStringifiedParams } from '~/lib/utils/routes';

export async function GET(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId, ...rest } = getStringifiedParams(query);

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendGetCartShipping(
    { cartId, query: rest },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (res.error.statusCode !== 404 && res.error.statusCode !== 539) {
    return new Response(null, { status: res.error.statusCode });
  } else {
    return new Response(
      JSON.stringify({ error: res.error, isSuccess: false }),
      {
        status: res.error.statusCode,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId, ...rest } = getStringifiedParams(query);
  const requestBody = await request.json();

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendCreateCartShipping(
    {
      cartId,
      input: requestBody,
      query: rest,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, { status: res.error.statusCode });
}

export async function PUT(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId, ...rest } = getStringifiedParams(query);
  const requestBody = await request.json();

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendUpdateCartShipping(
    {
      cartId,
      input: requestBody,
      query: rest,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(JSON.stringify(res.data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return new Response(null, { status: res.error.statusCode });
}

export async function DELETE(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { cartId, ...rest } = getStringifiedParams(query);

  if (!cartId) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query,
  });

  const res = await backendDeleteCartShipping(
    {
      cartId,
      query: rest,
    },
    extraQueryParams,
  );

  if (res.isSuccess) {
    return new Response(null, { status: 204 });
  }

  return new Response(null, { status: res.error.statusCode });
}
