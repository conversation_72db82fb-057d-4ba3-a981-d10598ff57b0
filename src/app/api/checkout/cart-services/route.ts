import { NextRequest } from 'next/server';

import { backendBootstrap } from '~/lib/backend/bootstrap';
import {
  backendCreateCartServices,
  backendGetCartServices,
} from '~/lib/backend/checkout/cart-services';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import logger from '~/lib/helpers/logger';
import { getStringifiedParams } from '~/lib/utils/routes';

export async function GET(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id } = getStringifiedParams(query);

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserTime: true,
    includeUserVwo: true,
    query,
  });

  const userSessionId = extraQueryParams.xUserSessionId;

  if (!userSessionId) {
    return new Response(null, { status: 401 });
  }

  try {
    const res = await backendGetCartServices(
      {
        id,
        input: {
          sessionId: userSessionId,
        },
      },
      extraQueryParams,
    );

    if (res.isSuccess && res.data) {
      return new Response(JSON.stringify(res.data), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(null, { status: 500 });
  } catch (error) {
    logger.error('Error in cart services API:', error);
    return new Response(null, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  await backendBootstrap();

  const searchParams = request.nextUrl.searchParams;
  const query = Object.fromEntries(searchParams);
  const { id } = getStringifiedParams(query);
  const requestBody = await request.json();

  if (!id) {
    return new Response(null, { status: 400 });
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserTime: true,
    includeUserVwo: true,
    query,
  });

  // Extract userSessionId from headers
  const userSessionId = extraQueryParams.xUserSessionId;

  if (!userSessionId) {
    return new Response(null, { status: 401 });
  }

  try {
    const res = await backendCreateCartServices(
      {
        id,
        input: {
          sessionId: userSessionId,
          ...requestBody,
        },
      },
      extraQueryParams,
    );

    if (res.isSuccess && res.data) {
      return new Response(JSON.stringify(res.data), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(null, { status: 500 });
  } catch (error) {
    logger.error('Error in cart services API:', error);
    return new Response(null, { status: 500 });
  }
}
